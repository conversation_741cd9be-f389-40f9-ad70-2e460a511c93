<script setup lang="ts">
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
import type CellValue from 'exceljs/index';

import { reactive } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space, Upload } from 'ant-design-vue';
import exceljs from 'exceljs';

type UploadApiConfig = {
  headers?: Record<string, any>;
  onUploadProgress?: (payload: { loaded: number; total: number }) => void;
  withCredentials?: boolean;
};

type UploadApi = (formData: Record<string, any>, config?: UploadApiConfig) => Promise<unknown>;

type DownloadTemplateApi = () => Promise<unknown> | void;

type TableField = {
  disable?: boolean;
  header: string;
  key: string;
  width?: number;
};

export interface TemplateData {
  title: string;
  startRow: number;
  path: string;
  tableField: TableField[];
}

interface ImportDataProps {
  title?: string;
  downloadTemplateApi?: DownloadTemplateApi;
  uploadApi?: UploadApi;
  icon?: string;
  multiple?: boolean;
  maxCount?: number;
  autoClose?: boolean;
  localImport?: boolean;
  templateData?: TemplateData;
}

const props = withDefaults(defineProps<ImportDataProps>(), {
  title: '导入',
  downloadTemplateApi: undefined,
  uploadApi: undefined,
  icon: '',
  multiple: false,
  maxCount: 1,
  autoClose: true,
  localImport: false,
  templateData: undefined,
});
const emit = defineEmits(['importSuccess', 'uploadSuccess']);
const loading = reactive({
  download: false,
});
const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  showConfirmButton: false,
  cancelText: '完成',
});
const openDialog = () => {
  modalApi.open();
};
const generateTemplate = () => {
  if (!props.templateData) return;
  if (props.templateData.path) {
    window.open(props.templateData.path, '_blank');
  } else {
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet();
    const columns: any[] = [];
    props.templateData.tableField.forEach((item) => {
      if (item.disable !== true) {
        columns.push({ header: item.header, key: item.key, width: item.width || 30, alignment: { wrapText: true } });
      }
    });
    worksheet.columns = columns;
    worksheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'fff2f2f2' } };
    });
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${props.templateData?.title ?? ''}导入模板.xlsx`;
      a.click();
    });
  }
};
const downloadTemplate = async () => {
  loading.download = true;
  if (props.templateData) {
    generateTemplate();
    loading.download = false;
  } else {
    try {
      if (props.downloadTemplateApi) {
        await props.downloadTemplateApi();
      }
    } finally {
      loading.download = false;
    }
  }
};

const handleImportFile = async (file: File) => {
  const workbook = new exceljs.Workbook();
  const buffer = await file.arrayBuffer();
  await workbook.xlsx.load(buffer);
  const worksheet = workbook.getWorksheet(1);
  const importData: { [property: string]: any }[] = [];
  const handleRow = (values: CellValue[] | { [key: string]: CellValue }) => {
    const objectData: { [property: string]: any } = {};
    values.forEach((value: CellValue, index: number) => {
      const keyInfo = props.templateData?.tableField[index - 1];
      if (keyInfo) {
        objectData[keyInfo.key] = value;
      }
    });
    return objectData;
  };
  if (worksheet) {
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber >= (props.templateData?.startRow ?? 0)) {
        importData.push(handleRow(row.values));
      }
    });
  }
  return importData;
};
const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'done') {
    emit('uploadSuccess', info.file.response);
    const allFilesDone = info.fileList.every((file: UploadFile) => file.status === 'done');

    if (allFilesDone) {
      if (props.autoClose) {
        modalApi.close();
      }
      message.success(props.localImport ? '所有文件解析成功' : '所有文件导入成功');

      const allSuccessResponses = info.fileList.filter((file) => file.status === 'done').map((file) => file.response);

      emit('importSuccess', allSuccessResponses);
    }
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} ${props.localImport ? '解析失败' : '上传失败'}`);
  }
};
const uploadFile = ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  const formData = { ...data };
  formData[filename] = file;
  if (props.localImport) {
    (async () => {
      try {
        const parsedData = await handleImportFile(file as File);
        if (onSuccess) {
          onSuccess(parsedData);
        }
      } catch (error: any) {
        if (onError) {
          onError(error);
        }
      }
    })();
  } else if (props.uploadApi) {
    props
      .uploadApi(formData, {
        withCredentials,
        headers,
        onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
          if (onProgress) {
            onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
          }
        },
      })
      .then((response: unknown) => {
        if (onSuccess) {
          onSuccess(response);
        }
      })
      .catch(onError);
  }
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
</script>

<template>
  <div>
    <Button type="primary" :icon="icon" @click="openDialog">导入</Button>
    <Modal :title="props.title" centered>
      <Space direction="vertical" size="middle">
        <Button v-if="props.downloadTemplateApi || localImport" @click="downloadTemplate">下载模板</Button>
        <Upload
          :custom-request="uploadFile"
          :multiple="multiple"
          :max-count="maxCount"
          name="file"
          @change="handleUploadChange"
        >
          <Button type="primary">上传文件</Button>
        </Upload>
      </Space>
    </Modal>
  </div>
</template>

<style></style>
