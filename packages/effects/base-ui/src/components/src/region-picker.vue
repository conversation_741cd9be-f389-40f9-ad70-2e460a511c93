<script setup lang="ts">
import type { CascaderProps } from 'ant-design-vue';
import type { DefaultOptionType } from 'ant-design-vue/es/vc-cascader';

import type { RegionInfo } from '@vben/types';

import { computed, ref } from 'vue';

import { Cascader } from 'ant-design-vue';
import { find } from 'lodash-es';

const props = defineProps({
  reginApi: { type: Function, required: true },
});

const province = defineModel('province', { type: [String, null] });
const city = defineModel('city', { type: [String, null] });
const district = defineModel('district', { type: [String, null] });
const districtCode = defineModel('districtCode', { type: [String, null] });

const value = computed({
  get() {
    const arr = [];
    if (province.value) arr[0] = province.value;
    if (city.value) arr[1] = city.value;
    if (district.value) arr[2] = district.value;
    return arr;
  },
  set(val: string[]) {
    province.value = val?.[0] || undefined;
    city.value = val?.[1] || undefined;
    district.value = val?.[2] || undefined;
    if (!val || val.length === 0) {
      districtCode.value = undefined;
    }
  },
});

const options = ref<RegionInfo[]>([]);
const fieldNames = {
  label: 'name',
  value: 'name',
  children: 'children',
};

const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  if (targetOption && !targetOption.children) {
    targetOption.loading = true;
    try {
      const res: RegionInfo[] = await props.reginApi({ parentId: targetOption.id });
      res.forEach((item) => {
        // levelType 为 '3' 表示最后一级（区/县），不需要再加载
        if (item.levelType !== '3') {
          item.isLeaf = false;
        }
      });
      targetOption.children = res.length > 0 ? res : undefined;
    } finally {
      targetOption.loading = false;
    }
    options.value = [...options.value];
  }
};

const changeRegion: CascaderProps['onChange'] = (_val, selectedOptions) => {
  const options = selectedOptions as DefaultOptionType[];
  const last = options?.[options.length - 1] as RegionInfo;
  districtCode.value = last?.id || undefined;
};

const handleReplay = async () => {
  if (!province.value) {
    return;
  }
  const provinceOption = find(options.value, { name: province.value });
  if (!provinceOption) {
    console.warn(`Initial province "${province.value}" not found in options.`);
    return;
  }

  await loadData([provinceOption]);

  if (!city.value || !provinceOption.children) {
    return;
  }

  const cityOption = find(provinceOption.children, { name: city.value });
  if (!cityOption) {
    console.warn(`Initial city "${city.value}" not found in options.`);
    return;
  }

  if (district.value) {
    await loadData([provinceOption, cityOption]);
  }
};

const init = async () => {
  // 加载第一级（省份）数据
  const res: RegionInfo[] = await props.reginApi({ parentId: '100000' });
  res.forEach((item) => {
    item.isLeaf = false;
  });
  options.value = res;
  await handleReplay();
};

init();
</script>

<template>
  <Cascader
    v-model:value="value"
    :options="options"
    :load-data="loadData"
    :field-names="fieldNames"
    change-on-select
    @change="changeRegion"
  />
</template>

<style scoped></style>
