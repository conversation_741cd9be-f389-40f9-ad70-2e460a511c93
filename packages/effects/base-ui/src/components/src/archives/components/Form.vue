<script lang="ts" setup>
import type { FormSchema } from '@vben/fe-ui';

import { computed, reactive } from 'vue';

import { BasicForm, BasicModal, useForm, useModalInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';

// import { create, getInfo, update } from '#/api/workFlow/document';
const props = defineProps({
  api: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['register', 'reload']);

const { createArchiveFolderApi, getArchiveFileInfoApi, renameArchiveFileApi } = props.api;

interface State {
  dataForm: any;
  id?: number;
  parentId: number;
  projectId: number;
}

const state = reactive<State>({
  dataForm: {},
  id: undefined,
  parentId: 0,
  projectId: 0,
});
const schemas: FormSchema[] = [
  {
    field: 'fileName',
    label: '',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
];
const getTitle = computed(() => (state.id ? '重命名文件' : '新建文件夹'));
const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({ labelWidth: 80, schemas });
const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);

function init(data: { id?: number; parentId?: number; projectId?: number }) {
  resetFields();
  if (data.id) {
    state.id = data.id;
  }
  state.projectId = data.projectId ?? 0;
  state.parentId = data.parentId ?? 0;
  if (state.id) {
    changeLoading(true);
    getArchiveFileInfoApi({ id: state.id }).then((res) => {
      state.dataForm = res;
      setFieldsValue({ fileName: state.dataForm.mainName });
      changeLoading(false);
    });
  }
}
async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: state.id,
    parentId: state.parentId,
    projectId: state.projectId,
  };
  const formMethod = state.id ? renameArchiveFileApi : createArchiveFolderApi;
  formMethod(query)
    .then(() => {
      message.success('保存成功');
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .finally(() => {
      changeOkLoading(false);
    });
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    show-ok-btn
    @ok="handleSubmit"
    destroy-on-close
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
