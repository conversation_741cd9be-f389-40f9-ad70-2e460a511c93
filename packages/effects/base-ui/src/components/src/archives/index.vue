<script setup lang="ts">
import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree';

import type { DriveFileInfo } from '@vben/types';

import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';

import { DocumentIcons, FilePreviewDialog, FileUploader } from '@vben/base-ui';
import { confirm } from '@vben/common-ui';
import { BasicForm, BasicTable, FeEmpty, ScrollContainer, useForm, useModal, useTable } from '@vben/fe-ui';
import { toFileSize } from '@vben/fe-ui/utils/fe';
import { downloadAndSaveWithAutoFileName } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import {
  AppstoreOutlined,
  BarsOutlined,
  CloudDownloadOutlined,
  DeleteOutlined,
  DeliveredProcedureOutlined,
  EditOutlined,
  FolderAddOutlined,
} from '@ant-design/icons-vue';
import {
  Breadcrumb,
  BreadcrumbItem,
  Button,
  Checkbox,
  CheckboxGroup,
  message,
  Space,
  Tooltip,
  Tree,
} from 'ant-design-vue';
import { find } from 'lodash-es';

import FolderTree from './components/FolderTree.vue';
import Form from './components/Form.vue';

const props = defineProps({
  projectId: {
    type: Number,
    required: true,
  },
  archiveApiGroup: {
    type: Object,
    default: () => ({}),
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const {
  checkArchiveFileApi,
  confirmArchiveFileApi,
  delArchiveFileApi,
  downloadArchiveFileApi,
  downloadArchiveMultiFileApi,
  getArchiveListApi,
  getArchiveTreeApi,
  getDownloadFileLinkApi,
  getPreviewFileExternalLink,
  preCheckArchiveFileApi,
  uploadArchiveFileApi,
} = props.archiveApiGroup;

interface ArchiveTreeInfo extends DataNode {
  folderId?: number;
  projectId?: number;
  children?: ArchiveTreeInfo[];
}

const { audioImg, blankImg, codeImg, excelImg, folderImg, imageImg, pdfImg, pptImg, rarImg, txtImg, wordImg } =
  DocumentIcons;

const dropZoneRef = ref<HTMLDivElement | null>(null);
const dragCounter = ref(0);
const activeKey = ref<'all' | 'shareOut' | 'shareTome' | 'trash'>('all');
const levelList = ref<{ fileName?: string; id: number }[]>([]);
const searchInfo = ref<{ keyword: string; parentId: number; projectId: number }>({
  keyword: '',
  parentId: 0,
  projectId: props.projectId,
});
const loading = ref(false);
const treeData = ref<ArchiveTreeInfo[]>([]);
const selectedKeys = ref<(number | string)[]>([]);
const expandedKeys = ref<(number | string)[]>([]);
const fileList = ref<DriveFileInfo[]>([]);
const selectedRowKeys = ref<number[]>([]);
const checkAll = ref(false);
const isIndeterminate = ref(false);
const showMode = ref(1);
const fileUploaderRef = ref();
const FilePreviewDialogRef = ref();

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowSelection: { type: 'checkbox' },
  clickToRowSelect: false,
  showTableSetting: false,
  pagination: false,
  immediate: false,
  canResize: false,
  // scroll: { y: undefined },
});

const [registerForm] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
  schemas: [
    {
      field: 'keyword',
      label: '关键字',
      component: 'Input',
      componentProps: {
        placeholder: '请输入关键词',
        submitOnPressEnter: true,
      },
    },
  ],
});

const [registerFormModal, { openModal: openFormModal }] = useModal();
const [registerFolderTree, { openModal: openFolderTree }] = useModal();

const getArchiveTree = async () => {
  if (!props.projectId) return;
  treeData.value = await getArchiveTreeApi({ projectId: props.projectId });
  if (treeData.value.length > 0 && treeData.value[0]?.key) {
    const node = treeData.value[0];
    selectedKeys.value = [node.key];
    expandedKeys.value = [node.key];
    if (node.projectId) {
      searchInfo.value.projectId = node.projectId;
    }
    levelList.value = [{ id: 0, fileName: node.title as string }];
    handleReset();
  }
};

const initData = () => {
  loading.value = true;
  selectedRowKeys.value = [];
  clearSelectedRowKeys();
  handleCheckedChange(selectedRowKeys.value);
  getArchiveListApi(searchInfo.value).then((res) => {
    fileList.value = res;
    loading.value = false;
  });
};

const handleTreeSelect = (_keys: (number | string)[], { node }: { node: ArchiveTreeInfo & EventDataNode }) => {
  const findPath = (
    tree: ArchiveTreeInfo[],
    key: number | string,
    path: ArchiveTreeInfo[] = [],
  ): ArchiveTreeInfo[] | null => {
    for (const item of tree) {
      const currentPath = [...path, item];
      if (item.key === key) {
        return currentPath;
      }
      if (item.children) {
        const result = findPath(item.children, key, currentPath);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  if (node?.projectId && node.key) {
    searchInfo.value.projectId = node.projectId;
    searchInfo.value.parentId = (node.folderId as number) || 0;

    const path = findPath(treeData.value, node.key);

    levelList.value = path
      ? path.map((p) => ({
          id: (p.folderId as number) || 0,
          fileName: p.title as string,
        }))
      : [{ id: (node.folderId as number) || 0, fileName: node.title as string }];

    handleReset();
  }
};

// function init() {
//   const activeItem = find(treeData.value, { key: selectedKeys.value[0] });
//   levelList.value = [{ id: 0, fileName: (activeItem?.title as string) || '我的文档' }];
//   searchInfo.value.parentId = 0;
//   resetFields();
//   initData();
// }

watch(
  () => props.projectId,
  () => {
    getArchiveTree();
  },
  { immediate: true },
);

const handleReturnToPrevious = () => {
  if (levelList.value.length > 1) {
    const previousItem = levelList.value[levelList.value.length - 2];
    if (previousItem) {
      handleJump(previousItem as { id: number }, levelList.value.length - 2);
    }
  }
};

const findNodeByFolderId = (nodes: ArchiveTreeInfo[], id: number): ArchiveTreeInfo | null => {
  for (const node of nodes) {
    if (id === 0) {
      if (node.projectId === searchInfo.value.projectId && !node.folderId) {
        return node;
      }
    } else if (node.folderId === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeByFolderId(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

const handleJump = (item: { id: number }, i: number) => {
  searchInfo.value.parentId = item.id;
  levelList.value = levelList.value.slice(0, i + 1);

  const targetNode = findNodeByFolderId(treeData.value, item.id);
  if (targetNode && targetNode.key) {
    selectedKeys.value = [targetNode.key];
  }

  handleReset();
};

const handleReset = () => {
  searchInfo.value.keyword = '';
  initData();
};

const handleCheckedChange = (val: any[]) => {
  const checkedCount = val.length;
  checkAll.value = !!checkedCount && checkedCount === fileList.value.length;
  isIndeterminate.value = !!checkedCount && checkedCount < fileList.value.length;
};

function handleSubmit(values: { keyword?: string }) {
  searchInfo.value.keyword = values?.keyword || '';
  if (searchInfo.value.keyword) {
    const activeItem = find(treeData.value, { key: selectedKeys.value[0] });
    levelList.value = [{ id: 0, fileName: (activeItem?.title as string) || '我的文档' }];
    searchInfo.value.parentId = 0;
  }
  initData();
}

function addFolder() {
  openFormModal(true, { parentId: searchInfo.value.parentId, projectId: searchInfo.value.projectId });
}
function toggleShowMode(type: number) {
  showMode.value = type;
  handleCheckedChange(selectedRowKeys.value);
}
function handleDownload() {
  let api = downloadArchiveMultiFileApi({ ids: selectedRowKeys.value });
  if (selectedRowKeys.value.length === 1) {
    const fileInfo = find(fileList.value, { id: selectedRowKeys.value[0] });
    api =
      fileInfo?.fileType === 1
        ? downloadArchiveMultiFileApi({ ids: selectedRowKeys.value })
        : downloadArchiveFileApi({ id: selectedRowKeys.value[0] as number });
  }
  api.then((res) => {
    downloadAndSaveWithAutoFileName({ url: res });
  });
}
async function confirmArchive() {
  await confirm('您确定要把所选文件归档, 是否继续?', '提示');
  confirmArchiveFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('归档成功');
    initData();
  });
}
async function handleDelete() {
  await confirm('您确定要把所选文件放入回收站, 是否继续?', '提示');
  delArchiveFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('删除成功');
    initData();
  });
}
function handleRename() {
  openFormModal(true, { parentId: searchInfo.value.parentId, id: selectedRowKeys.value[0] });
}
function handleMoveTo() {
  openFolderTree(true, {
    ids: selectedRowKeys.value,
    parentId: searchInfo.value.parentId,
    projectId: searchInfo.value.projectId,
  });
}
function onSelectionChange({ keys }: { keys: number[] }) {
  selectedRowKeys.value = keys;
}

const allColumns = [
  { title: '文件名', dataIndex: 'fileName', key: 'fileName' },
  // { title: '', dataIndex: 'isShare', key: 'isShare', width: 35 },
  { title: '是否归档', dataIndex: 'isArchived', key: 'isArchived', width: 90 },
  { title: '大小', dataIndex: 'fileSize', key: 'fileSize', width: 90 },
  { title: '创建日期', dataIndex: 'createTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
];

function getColumns() {
  return allColumns;
}

const getTableBindValue = computed(() => ({
  loading: loading.value,
  onSelectionChange,
  dataSource: fileList.value,
  columns: getColumns(),
}));

const fileTypeMap = new Map([
  [['7z', 'arj', 'rar', 'z', 'zip'], rarImg],
  [['avi', 'flv', 'mkv', 'mov', 'mp3', 'mp4', 'mpeg', 'mpg', 'ram', 'rm', 'rmvb', 'swf', 'wma', 'wmv'], audioImg],
  [['bmp', 'gif', 'jpeg', 'jpg', 'png'], imageImg],
  [['cs', 'html', 'xml'], codeImg],
  [['doc', 'docx'], wordImg],
  [['log', 'txt'], txtImg],
  [['pdf'], pdfImg],
  [['ppt', 'pptx'], pptImg],
  [['xls', 'xlsx'], excelImg],
]);
function getRecordImg(ext?: string) {
  if (!ext) return folderImg;
  const extension = ext.replace('.', '').toLowerCase();
  for (const [types, img] of fileTypeMap.entries()) {
    if (types.includes(extension)) {
      return img;
    }
  }
  return blankImg;
}
function handlePreview(record: DriveFileInfo) {
  FilePreviewDialogRef.value.init(record.fileId);
}

// --- 修改：为 openFolder 函数增加树联动逻辑 ---
function openFolder(record: DriveFileInfo) {
  if (record.id) {
    // --- 新增逻辑：同步更新左侧树的状态 ---
    const targetNode = findNodeByFolderId(treeData.value, record.id);
    if (targetNode && targetNode.key) {
      // 1. 同步树的选中项
      selectedKeys.value = [targetNode.key];
      // 2. 确保该节点在树中是展开的，以便查看其子节点 (使用 Set 去重)
      expandedKeys.value = [...new Set([targetNode.key, ...expandedKeys.value])];
    }
    // --- 结束新增逻辑 ---

    // 原有逻辑
    searchInfo.value.parentId = record.id;
    levelList.value.push({ id: record.id, fileName: record.fileName });
    selectedRowKeys.value = [];
    handleReset();
  }
}

function onRecordClick(record: DriveFileInfo) {
  if (['shareOut', 'trash'].includes(activeKey.value)) return;
  record.fileType ? openFolder(record) : handlePreview(record);
}
function handleCheckAllChange(e: { target: { checked: any } }) {
  const val = e.target.checked;
  selectedRowKeys.value = val ? fileList.value.map((o) => o.id as number) : [];
  isIndeterminate.value = false;
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value++;
  if (dragCounter.value === 1) {
    dropZoneRef.value?.classList.add('drag-over-active');
  }
};
const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value--;
  if (dragCounter.value === 0) {
    dropZoneRef.value?.classList.remove('drag-over-active');
  }
};
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value = 0;
  dropZoneRef.value?.classList.remove('drag-over-active');
  const items = event.dataTransfer?.items;
  if (items) {
    for (const item of items) {
      const entry = item.webkitGetAsEntry();
      if (entry && entry.isDirectory) {
        message.warning('不支持拖拽文件夹上传，请直接拖拽文件。');
        return;
      }
    }
  }
  const files = event.dataTransfer?.files;
  if (fileUploaderRef.value && files && files.length > 0) {
    fileUploaderRef.value.uploadFiles([...files]);
  }
};
const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
};
onMounted(() => {
  if (dropZoneRef.value) {
    dropZoneRef.value.addEventListener('dragenter', handleDragEnter);
    dropZoneRef.value.addEventListener('dragleave', handleDragLeave);
    dropZoneRef.value.addEventListener('drop', handleDrop);
    dropZoneRef.value.addEventListener('dragover', handleDragOver);
  }
});
onUnmounted(() => {
  if (dropZoneRef.value) {
    dropZoneRef.value.removeEventListener('dragenter', handleDragEnter);
    dropZoneRef.value.removeEventListener('dragleave', handleDragLeave);
    dropZoneRef.value.removeEventListener('drop', handleDrop);
    dropZoneRef.value.removeEventListener('dragover', handleDragOver);
  }
});
</script>

<template>
  <div>
    <div class="document-wrapper test flex" ref="dropZoneRef">
      <div class="mr-3 w-[300px] shrink-0 overflow-y-auto border-r p-3">
        <Tree
          v-if="treeData.length > 0"
          v-model:selected-keys="selectedKeys"
          v-model:expanded-keys="expandedKeys"
          :show-line="true"
          :tree-data="treeData"
          @select="handleTreeSelect"
        >
          <template #title="{ dataRef }">
            <div class="flex items-center">
              <VbenIcon
                :icon="
                  (dataRef as ArchiveTreeInfo).folderId ? 'ant-design:folder-open-filled' : 'ant-design:project-filled'
                "
                class="mr-2"
                :color="(dataRef as ArchiveTreeInfo).folderId ? '#ffc53d' : '#40a9ff'"
              />
              <span>{{ dataRef.title }}</span>
            </div>
          </template>
        </Tree>
        <FeEmpty v-else class="flex items-center justify-center" description="暂无项目文档" />
      </div>

      <div class="document-container">
        <Breadcrumb class="mb-3">
          <BreadcrumbItem v-if="levelList.length > 1" @click="handleReturnToPrevious">
            <a>返回上一级</a>
          </BreadcrumbItem>
          <BreadcrumbItem v-for="(item, i) in levelList" :key="i">
            <span v-if="i + 1 >= levelList.length">{{ item.fileName }}</span>
            <a v-else @click="handleJump(item, i)">{{ item.fileName }}</a>
          </BreadcrumbItem>
        </Breadcrumb>
        <div class="fe-common-search-box">
          <BasicForm class="search-form" @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
          <div class="fe-common-search-box-right">
            <template v-if="selectedRowKeys.length === 0">
              <template v-if="activeKey === 'all'">
                <Button v-if="!readonly" :icon="h(FolderAddOutlined)" @click="addFolder()" class="mr-2">
                  新建文件夹
                </Button>
                <FileUploader
                  ref="fileUploaderRef"
                  v-if="!readonly"
                  :upload-api="uploadArchiveFileApi"
                  :pre-check-api="preCheckArchiveFileApi"
                  :check-api="checkArchiveFileApi"
                  :folder-id="searchInfo.parentId"
                  :params="{ projectId: searchInfo.projectId }"
                  class="mr-2"
                  @all-completed="initData"
                />
              </template>
              <Tooltip>
                <template #title>{{ showMode === 1 ? '缩略模式' : '列表模式' }}</template>
                <a-button v-show="showMode === 1" @click="toggleShowMode(2)" :icon="h(AppstoreOutlined)" />
                <a-button v-show="showMode === 2" @click="toggleShowMode(1)" :icon="h(BarsOutlined)" />
              </Tooltip>
            </template>
            <template v-else>
              <Space.Compact block>
                <Button v-if="!readonly" :icon="h(CloudDownloadOutlined)" @click="confirmArchive">确认归档</Button>
                <Button :icon="h(CloudDownloadOutlined)" @click="handleDownload">下载</Button>
                <Button v-if="!readonly" :icon="h(DeleteOutlined)" @click="handleDelete">删除</Button>
                <Button :icon="h(EditOutlined)" @click="handleRename" v-if="selectedRowKeys.length === 1 && !readonly">
                  重命名
                </Button>
                <Button v-if="!readonly" :icon="h(DeliveredProcedureOutlined)" @click="handleMoveTo">移动</Button>
              </Space.Compact>
            </template>
          </div>
        </div>
        <BasicTable @register="registerTable" v-bind="getTableBindValue" v-show="showMode === 1">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fileName'">
              <span class="document-fileName" :class="{ 'link-fullName': record.type }" @click="onRecordClick(record)">
                <img :src="getRecordImg(record.fileExtension)" class="file-img" alt="" />
                {{ record.fileName }}
              </span>
            </template>
            <!--<template v-if="column.key === 'isShare'">-->
            <!--  <span v-if="record.isShare" title="共享文件">-->
            <!--    <VbenIcon icon="gg:share" />-->
            <!--  </span>-->
            <!--  <span v-else></span>-->
            <!--</template>-->
            <template v-if="column.key === 'isArchived'">
              <span v-if="record.fileType === 0">{{ record.isArchived ? '是' : '否' }}</span>
              <span v-else></span>
            </template>
            <template v-if="column.key === 'fileSize'">
              {{ toFileSize(record.fileSize) }}
            </template>
          </template>
        </BasicTable>
        <div class="document-list-header" v-show="showMode === 2">
          <Checkbox
            :indeterminate="isIndeterminate"
            v-model:checked="checkAll"
            :disabled="fileList.length === 0"
            @change="handleCheckAllChange"
          >
            全选
          </Checkbox>
        </div>
        <CheckboxGroup
          v-model:value="selectedRowKeys"
          class="document-list"
          @change="handleCheckedChange"
          v-show="showMode === 2"
        >
          <ScrollContainer v-loading="loading">
            <div class="document-list-main">
              <div
                class="document-item"
                :class="{ active: record.id && selectedRowKeys.includes(record.id) }"
                v-for="record in fileList"
                :key="record.id"
                @click="onRecordClick(record)"
              >
                <img :src="getRecordImg(record.fileExtension)" class="document-item-img" alt="图标" />
                <p class="document-item-title" :title="record.fileName">{{ record.fileName }}</p>
                <div class="check-icon" @click.stop>
                  <Checkbox :value="record.id" />
                </div>
              </div>
            </div>
            <FeEmpty v-if="fileList.length === 0" />
          </ScrollContainer>
        </CheckboxGroup>
      </div>
    </div>
    <Form :api="archiveApiGroup" @register="registerFormModal" @reload="initData" />
    <FolderTree :api="archiveApiGroup" @register="registerFolderTree" @reload="initData" />
    <FilePreviewDialog
      ref="FilePreviewDialogRef"
      :preview-api="getPreviewFileExternalLink"
      :download-api="getDownloadFileLinkApi"
    />
  </div>
</template>

<style lang="less">
//@import '#/style/index.less';
@import '@vben/fe-ui/style/index.less';

.document-wrapper {
  background-color: @app-content-background;

  .ant-tree-switcher-noop > span {
    display: none;
  }

  :deep(.ant-table-container),
  .ant-table-container {
    .ant-table-cell::before {
      display: none !important;
    }
  }
  .ant-tabs-content-holder {
    display: none;
  }
  .ant-checkbox-group {
    cursor: default;
  }
  .document-container {
    flex: 1;
    padding-top: 20px;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .fe-common-search-box {
    margin-bottom: 10px;
    position: relative;
    .fe-common-search-box-right {
      position: absolute;
      right: 10px;
      top: 0;
      display: flex;
      align-items: center;
      .mode-icon {
        margin-left: 10px;
        font-size: 18px;
        line-height: 32px;
        cursor: pointer;
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  .document-list-header {
    margin-top: -10px;
    margin-right: 10px;
    line-height: 40px;
    flex-shrink: 0;
    border-bottom: 1px solid @border-color-base1;
  }
  .document-list {
    flex: 1;
    width: 100%;
    overflow: hidden;
    padding-bottom: 10px;
    .document-list-main {
      padding: 20px 10px 0 0;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-content: flex-start;
      flex-wrap: wrap;
    }
    .document-item {
      width: 100px;
      height: 100px;
      border-radius: var(--border-radius);
      overflow: hidden;
      margin: 0 20px 40px;
      padding: 5px;
      cursor: pointer;
      position: relative;
      &:hover {
        background-color: @app-content-background;
        .check-icon {
          display: block;
        }
      }
      &.active {
        .check-icon {
          display: block;
        }
      }
      .document-item-img {
        width: 60px;
        height: 60px;
        margin: 0 auto 6px;
      }
      .document-item-title {
        color: @text-color-label;
        font-size: 14px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .check-icon {
        position: absolute;
        top: 2px;
        right: 4px;
        display: none;
      }
    }
  }
  .document-fileName {
    cursor: pointer;
    &.link-fullName {
      &:hover {
        color: @primary-color;
      }
    }
    .file-img {
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: -3px;
    }
  }
}

.document-wrapper {
  position: relative;
}

.drag-over-active {
  &::after {
    content: '拖放到此处以上传';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(24, 144, 255, 0.1);
    border: 2px dashed #1890ff;
    border-radius: 8px;
    box-sizing: border-box;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 500;
    color: #1890ff;
    pointer-events: none;
  }
}
</style>
