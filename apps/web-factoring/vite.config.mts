import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/jxct/api': {
            changeOrigin: true,
            // mock代理目标地址
            // target: 'http://blxt.jxctjtgyl.com.cn',
            target: 'http://*************',
            ws: true,
          },
          // '/jxct/api': {
          //   changeOrigin: true,
          //   rewrite: (path) => path.replace(/^\/jxct\/api/, ''),
          //   // mock代理目标地址
          //   target: 'http://localhost:5320/api',
          //   ws: true,
          // },
        },
      },
    },
  };
});
