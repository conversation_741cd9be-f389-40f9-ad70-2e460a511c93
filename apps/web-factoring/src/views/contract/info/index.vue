<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OverviewInfo } from '#/api';

import { ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';
import { isEmpty } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  contractChangeStatusApi,
  delContractApi,
  getContractListApi,
  getCreditApplyListApi,
  getProjectListApi,
  uploadSealContract,
} from '#/api';
import { ContractList } from '#/components';
import OverviewDetail from '#/views/project/overview/overview-project-contract-detail.vue';
import OverviewEdit from '#/views/project/overview/overview-project-contract.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'Input',
      fieldName: 'projectApplyName',
      label: '用信名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'projectApplyName', title: '用信名称', minWidth: 200 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_STATUS',
        },
      },
      minWidth: 120,
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 'auto',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getContractListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const edit = (row: OverviewInfo) => {
  openFormPopup(true, row);
};
const change = async (row: OverviewInfo) => {
  await contractChangeStatusApi(row.id as number);
  const data = { ...row };
  await gridApi.reload();
  openFormPopup(true, data);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: OverviewInfo) => {
  openDetailPopup(true, row);
};

const ProjectFormRef = ref();
const add = async () => {
  modalApi.open();
};

const filterOption = (input: string, option: any) => {
  return option.realName.toLowerCase().includes(input.toLowerCase());
};

const projectOptions = ref<any[]>([]);
const applyOptions = ref<any[]>([]);
const getProjectList = async () => {
  const res = await getProjectListApi({ status: 'EFFECTIVE', isMeetingCompleted: 1, isContractFlag: 1 });
  projectOptions.value = res;
};
getProjectList();

const getApplyList = async () => {
  const res = await getCreditApplyListApi({
    status: 'EFFECTIVE',
    isMeetingCompleted: 1,
    isContractFlag: 1,
    projectId: projectForm.value.projectId,
  });
  applyOptions.value = res;
};

const selectProject = () => {
  const project = projectOptions.value.find((item: object) => item.id === projectForm.value.projectId);
  projectForm.value.projectName = project?.projectName;
  projectForm.value.projectType = project?.projectType;
  if (!isEmpty(applyOptions)) {
    applyOptions.value = [];
  }
  if (projectForm.value.projectApplyId) {
    projectForm.value.projectApplyId = '';
  }
  getApplyList();
};

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await ProjectFormRef.value.validate();
    const apply = applyOptions.value.find((item: object) => item.id === projectForm.value.projectApplyId);
    projectForm.value.projectApplyName = apply?.projectCreditApplyName;
    await modalApi.close();
    openFormPopup(true, projectForm.value);
  },
  onClosed: () => {
    ProjectFormRef.value.resetFields();
    projectForm.value = {};
  },
});
const projectForm = ref<OverviewInfo>({});
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  projectApplyId: [{ required: true, message: '请选择关联用信申请', trigger: 'change' }],
};

// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: OverviewInfo = {
        id: params.id,
      };
      edit(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: OverviewInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: OverviewInfo = {
        id: params.id,
        pageType: 'audit',
        formKey: params.formKey,
      };
      audit(data);
    },
  },
});
const audit = (row: any) => {
  const data = { ...row, pageType: 'audit' };
  openDetailPopup(true, data);
};

const del = (row: OverviewInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该合同，是否继续？',
    async onOk() {
      await delContractApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
      getProjectList();
    },
  });
};

const uploadInfo = async (row: any) => {
  uploadForm.value = row;
  modalContractApi.open();
};
const [ModalContract, modalContractApi] = useVbenModal({
  onConfirm: async () => {
    await uploadSealContract(uploadForm.value);
    message.success($t('base.resSuccess'));
    await gridApi.reload();
    await modalContractApi.close();
  },
  onClosed: () => {
    uploadForm.value = {};
  },
});
const uploadForm = ref({});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add"> {{ $t('base.add') }} </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
          <a-typography-link
            v-if="['EFFECTIVE'].includes(row.status) && row.survivalStatus !== 'settled'"
            @click="change(row)"
          >
            变更
          </a-typography-link>
          <a-typography-link
            v-if="
              (['REVIEWING'].includes(row.reviewStatus) || ['EFFECTIVE'].includes(row.status)) &&
              row.survivalStatus !== 'settled'
            "
            @click="uploadInfo(row)"
          >
            上传盖章合同
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <OverviewEdit @register="registerForm" @ok="editSuccess" />
    <OverviewDetail @register="registerDetail" @ok="editSuccess" />
    <Modal title="选择项目">
      <a-form ref="ProjectFormRef" :model="projectForm" :rules="rules">
        <a-form-item label="关联项目" name="projectId">
          <a-select
            v-model:value="projectForm.projectId"
            show-search
            :options="projectOptions"
            :field-names="{ label: 'projectName', value: 'id' }"
            :filter-option="filterOption"
            @change="selectProject"
          />
        </a-form-item>
        <a-form-item label="关联用信申请" name="projectApplyId" v-if="projectForm.projectType === 'comprehensive'">
          <a-select
            v-model:value="projectForm.projectApplyId"
            show-search
            :options="applyOptions"
            :field-names="{ label: 'projectCreditApplyName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
      </a-form>
    </Modal>
    <ModalContract title="上传盖章合同" class="w-[800px]">
      <ContractList
        v-model="uploadForm.sealContractList"
        :business-id="uploadForm.id"
        :business-type="
          uploadForm.projectType === 'comprehensive' ? 'FCT_PROJECT_APPLY_CONTRACT_SEAL' : 'FCT_PROJECT_CONTRACT_SEAL'
        "
        edit-mode
      />
    </ModalContract>
  </Page>
</template>

<style></style>
