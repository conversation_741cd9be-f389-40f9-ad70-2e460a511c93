<script lang="ts" setup>
import type { DriveFileInfo } from '@vben/types';

import type { ContractInfo, ContractTemplateInfo } from '#/api';

import { computed, defineEmits, ref } from 'vue';

import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { useDictStore } from '@vben/stores';

import { FormItemRest, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { BaseFilePickList } from '#/adapter/base-ui';
import {
  copyFileApi,
  getContractClassifyListApi,
  getContractTemplateListApi,
  saveBusinessContractSignApi,
  saveTempContractSignApi,
} from '#/api';

const props = defineProps({
  businessId: {
    type: Number,
    default: null,
  },
  businessType: {
    type: String,
    required: true,
  },
});
const emit = defineEmits<Emits>();
// 定义 Emits 接口
interface Emits {
  (event: 'success', payload: ContractInfo): void;
}
const colSpanProp = COL_SPAN_PROP;
const dictStore = useDictStore();
const isTemp = computed(() => !props.businessId);
const [Modal, modalApi] = useVbenModal({
  confirmText: '保存',
  onConfirm: () => {
    confirm();
  },
  onClosed: () => {
    close();
  },
});

const contractForm = ref<ContractInfo>({
  createType: '2',
});
const rules = {
  contractName: [{ required: true, message: '请输入合同名称' }],
  // contractCode: [{ required: true, message: '请输入合同编码' }],
  fileId: [{ required: true, message: '请上传合同文件', trigger: 'change' }],
  templateId: [{ required: true, message: '请选择合同模板', trigger: 'change' }],
  createType: [{ required: true, message: '请选择创建方式', trigger: 'change' }],
};
const create = async () => {
  modalApi.open();
  templateList.value = await getContractTemplateListApi();
};

const templateList = ref<ContractTemplateInfo[]>([]);
const ContractFormRef = ref();
const templateInfo = ref<ContractTemplateInfo>();
const confirm = async () => {
  await ContractFormRef.value.validate();
  const formData: ContractInfo = cloneDeep(contractForm.value);
  if (formData.createType === '2' && templateInfo.value && templateInfo.value.fileId) {
    const res = await copyFileApi({ id: templateInfo.value.fileId });
    formData.fileId = res.id;
  }
  const saveApi = isTemp.value ? saveTempContractSignApi : saveBusinessContractSignApi;
  const res = await saveApi(formData);
  emit('success', res);
  await modalApi.close();
};

const close = () => {
  contractForm.value = {
    createType: '2',
  };
};

const changeTemplateId = async (_id: number, option: ContractTemplateInfo) => {
  templateInfo.value = option;
  if (!contractForm.value.contractName || contractForm.value.contractName === '') {
    contractForm.value.contractName = templateInfo.value.templateName;
  }
};
const pickFile = async (data: { file: DriveFileInfo; id: number }) => {
  if (!contractForm.value.contractName || contractForm.value.contractName === '') {
    contractForm.value.contractName = data.file.mainName;
  }
};
// const changeCreateType = async (value: string) => {
//   if (value === '1') {
//     delete contractForm.value.templateId;
//   }
//   delete contractForm.value.fileId;
// };

defineExpose({
  create,
  modalApi,
});
</script>

<template>
  <Modal title="新增合同" class="w-[1200px]">
    <div class="form-area">
      <div class="form-inner">
        <a-form ref="ContractFormRef" :model="contractForm" :rules="rules" v-bind="FORM_PROP">
          <a-row>
            <a-col v-bind="colSpanProp">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="contractForm.contractName" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpanProp">
              <a-form-item label="合同编码" name="contractCode">
                <a-input v-model:value="contractForm.contractCode" placeholder="留空则自动生成" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpanProp">
              <a-form-item label="用章类型" name="sealType">
                <a-select v-model:value="contractForm.signMethod" :options="dictStore.getDictList('SEAL_TYPE')" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpanProp">
              <a-form-item label="合同分类" name="categoryId">
                <ApiComponent
                  :component="Select"
                  v-model="contractForm.categoryId as unknown as string"
                  :api="getContractClassifyListApi"
                  label-field="categoryName"
                  value-field="id"
                  model-prop-name="value"
                />
              </a-form-item>
            </a-col>
            <!--<a-col v-bind="colSpanProp">-->
            <!--  <a-form-item label="创建方式" name="createType">-->
            <!--    <a-select-->
            <!--      v-model:value="contractForm.createType"-->
            <!--      :options="[-->
            <!--        { label: '上传文档', value: '1' },-->
            <!--        { label: '选择模板', value: '2' },-->
            <!--      ]"-->
            <!--      @change="changeCreateType"-->
            <!--    />-->
            <!--  </a-form-item>-->
            <!--</a-col>-->
            <a-col v-if="contractForm.createType === '1'" v-bind="colSpanProp">
              <a-form-item name="fileId" label="合同文件">
                <FormItemRest>
                  <BaseFilePickList v-model="contractForm.fileId" @pick="pickFile" />
                </FormItemRest>
              </a-form-item>
            </a-col>
            <template v-else>
              <a-col v-bind="colSpanProp">
                <a-form-item name="templateId" label="合同模板">
                  <a-select
                    v-model:value="contractForm.templateId"
                    :options="templateList"
                    :field-names="{ label: 'templateName', value: 'id' }"
                    @change="changeTemplateId"
                  />
                </a-form-item>
              </a-col>
            </template>
          </a-row>
        </a-form>
      </div>
    </div>
  </Modal>
</template>

<style lang="scss"></style>
