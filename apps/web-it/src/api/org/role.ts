import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface RoleInfo extends BaseDataParams {
  // 编码, 可选
  code?: string;
  // 名称, 可选
  name?: string;
  // 描述, 可选
  description?: string;
  // 全局标识, 可选
  globalMark?: number;
  // 组织Id列表, 可选
  organIdList?: string[];
  // 排序, 可选
  sortCode?: number;
  // 是否启用, 可选
  enabled?: number;
  // 扩展属性, 可选
  propertyJson?: string;
}

export function getRolePageListApi(params: PageListParams) {
  return requestClient.get('/upms/role/page', { params });
}
export function addRoleApi(data: RoleInfo) {
  return requestClient.post('/upms/role/add', data);
}
export function editRoleApi(data: RoleInfo) {
  return requestClient.post('/upms/role/edit', data);
}
export function deleteRoleApi(id: number) {
  return requestClient.post('/upms/role/delete', {}, { params: { id } });
}
export function getRoleListByOrgIdsApi(orgIds: string[]) {
  return requestClient.post('/upms/role/list_by_org_ids', orgIds);
}
export function getRoleListApi(params?: { name: string }) {
  return requestClient.get('/upms/role/list', { params });
}
