<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { BpmTaskApi } from '#/api/bpm/task';

import { Page } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { getTaskTodoPage } from '#/api/bpm/task';
import { router } from '#/router';

import { useGridColumns, useGridFormSchema } from './data';

defineOptions({ name: 'BpmTodoTask' });

/** 办理任务 */
function handleAudit(row: BpmTaskApi.TaskManager) {
  if (row.formKey) {
    const formCustomViewPath = row.formCustomViewPath;
    const businessKey = row.businessKey;
    const formKey = row.formKey;
    if (formCustomViewPath) {
      const params = new URLSearchParams();
      params.append('modal', 'audit');
      params.append('formKey', formKey);
      params.append('id', businessKey);
      const url = `${formCustomViewPath}?${params.toString()}`;
      window.open(url);
    }
  } else {
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id,
      },
    });
  }
}

const [Grid] = useVbenVxeGrid({
  formOptions: defineFormOptions({
    schema: useGridFormSchema(),
  }),
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getTaskTodoPage({
            pageNo: page.currentPage,
            size: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
    },
    cellConfig: {
      height: 64,
    },
  } as VxeTableGridOptions<BpmTaskApi.Task>,
});
</script>

<template>
  <Page auto-content-height>
    <Grid table-title="待办任务">
      <template #actions="{ row }">
        <TableAction
          :key="row.id"
          :actions="[
            {
              label: '办理',
              type: 'link',
              icon: ACTION_ICON.VIEW,
              // auth: ['bpm:task:query'],
              onClick: handleAudit.bind(null, row),
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
