<script setup lang="ts">
import type { Key } from 'ant-design-vue/es/_util/type';

import { reactive, ref } from 'vue';

import { formatMoney } from '@vben/utils';

import { Modal, Table } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

const props = withDefaults(
  defineProps<{
    rowKey?: string;
  }>(),
  {
    rowKey: 'sourceItemNumber',
  },
);
const columns = [
  { title: '行号', dataIndex: 'itemNumber', key: 'itemNumber', width: 100 },
  { title: '商品名称', dataIndex: 'productName', key: 'productName', width: 100, ellipsis: true },
  { title: '商品编码', dataIndex: 'productCode', key: 'productCode', width: 100 },
  { title: '商品别名', dataIndex: 'productAlias', key: 'productAlias', width: 100 },
  { title: '规格型号', dataIndex: 'specifications', key: 'specifications', width: 100 },
  { title: '单位', dataIndex: 'measureUnit', key: 'measureUnit', width: 100 },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100 },
  {
    title: '含税单价',
    dataIndex: 'priceWithTax',
    key: 'priceWithTax',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '税率(%)',
    dataIndex: 'taxRate',
    key: 'taxRate',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '含税金额',
    dataIndex: 'amountWithTax',
    key: 'amountWithTax',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '不含税金额',
    dataIndex: 'amountWithoutTax',
    key: 'amountWithoutTax',
    width: 150,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '税额',
    dataIndex: 'taxAmount',
    key: 'taxAmount',
    width: 100,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  {
    title: '销售含税单价',
    dataIndex: 'sourcePrice',
    key: 'sourcePrice',
    width: 150,
    customRender: ({ text }: { text: number }) => formatMoney(text),
  },
  { title: '备注', dataIndex: 'remarks', width: 200, key: 'remarks' },
];
const source = ref<any[]>([]);
let globalResolve: any;
let globalReject: any;
const state = reactive({
  visible: false,
});
const selectedRowKeys = ref<Key[]>([]);
const selectedRowList = ref<any[]>([]);
const pick = async (data: any[] = [], selectedKeys: any[] = []) => {
  return new Promise((resolve, reject) => {
    source.value = data;
    selectedRowKeys.value = cloneDeep(selectedKeys);
    selectedRowList.value = data.filter((item) => selectedKeys.includes(item[props.rowKey]));
    globalResolve = resolve;
    globalReject = reject;
    state.visible = true;
  });
};
const onSelectChange = (keys: Key[], selectedRows: any[]) => {
  selectedRowKeys.value = keys;
  selectedRowList.value = selectedRows;
};
const handleOk = () => {
  state.visible = false;
  if (globalResolve) {
    const list = cloneDeep(selectedRowList.value);
    list.forEach((item: any) => {
      delete item.id;
      delete item.itemNumber;
    });
    globalResolve(list);
  }
  globalResolve = null;
  globalReject = null;
};
const close = () => {
  selectedRowKeys.value = [];
  selectedRowList.value = [];
  if (globalReject) {
    globalReject();
  }
  globalResolve = null;
  globalReject = null;
};
const onRowClick = (record: any) => {
  const key = record[props.rowKey];
  const index = selectedRowKeys.value.indexOf(key);
  if (index === -1) {
    selectedRowKeys.value.push(key);
    selectedRowList.value.push(record);
  } else {
    selectedRowKeys.value.splice(index, 1);
    selectedRowList.value.splice(index, 1);
  }
};
defineExpose({
  pick,
});
</script>

<template>
  <Modal v-model:open="state.visible" title="商品选择" width="1000px" @ok="handleOk" @close="close">
    <Table
      :columns="columns"
      :data-source="source"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange, fixed: true }"
      :row-key="rowKey"
      table-layout="fixed"
      :scroll="{ x: '100%' }"
      :custom-row="(record) => ({ onClick: () => onRowClick(record) })"
    />
  </Modal>
</template>

<style></style>
