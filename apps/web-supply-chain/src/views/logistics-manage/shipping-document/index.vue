<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ShipmentBaseInfo, SignShipment } from '#/api';

import { h, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  Button,
  DatePicker,
  Descriptions,
  DescriptionsItem,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Space,
  Textarea,
  TypographyLink,
} from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteShipmentApi, getShipmentPageApi, getSignDetailApi, signShipmentApi } from '#/api';

import Create from './create.vue';
import Detail from './detail.vue';

const dictStore = useDictStore();

const sortKey = ref<string>('create_time');

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'shipmentCode',
      label: '发运单编号',
    },
    {
      component: 'Select',
      fieldName: 'transportMethod',
      label: '运输方式',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编码',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Select',
      fieldName: 'carrierCompanyName',
      label: '运输企业名称',
      // componentProps: {
      //   options: dictStore.getDictList('supplier_company'), // 补全字典类型
      // },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'warehouseName',
    //   label: '关联单据类型',
    //   // componentProps: {
    //   //   options: dictStore.getDictList('purchaser_company'), // 补全字典类型
    //   // },
    // },
    // {
    //   component: 'Input',
    //   fieldName: 'detailAddress',
    //   label: '关联单据编号',
    // },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'DateRangePicker',
      fieldName: 'shipmentDate',
      label: '发运日期',
    },
  ],
  fieldMappingTime: [['shipmentDate', ['shipmentStartDate', 'shipmentEndDate'], 'YYYY-MM-DD hh:mm:ss']],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'shipmentCode', title: '发货单编号' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    { field: 'carrierCompanyName', title: '运输企业' },
    {
      field: 'shipmentType',
      title: '发运类型',
      cellRender: {
        name: 'CellStatus',
        props: { code: 'TRANSPORT_TYPE' },
      },
    },
    // {
    //   field: 'transportMethod',
    //   title: '运输方式',
    //   cellRender: { name: 'CellStatus', props: { code: 'TRANSPORT_MODE' } },
    // },
    { field: 'consigneeCompanyName', title: '收货企业' },
    { field: 'billingCompanyName', title: '结算企业' },
    { field: 'shipmentDate', title: '发运日期' },
    // { field: 'plannedDeliveryDate', title: '收货时间' },
    // { field: 'status', title: '状态', cellRender: { name: 'CellStatus', props: { code: 'BUS_STATUS' } } },
    { field: 'signFlag', title: '收货状态', cellRender: { name: 'CellStatus', props: { code: 'SIGN_STATUS' } } },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
      showOverflow: false,
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getShipmentPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetailForm, { openPopup: openDetailPopup }] = usePopup();

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: ShipmentBaseInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});

// 创建
const add = () => {
  openFormPopup(true, {});
};

// 编辑
// const edit = (row: ShipmentBaseInfo) => {
//   openFormPopup(true, row);
// };

// 查看
const detail = (row: ShipmentBaseInfo) => {
  openDetailPopup(true, row);
};

// 删除
const del = () => {
  const rows = gridApi.grid.getCheckboxRecords();
  if (rows.length === 0) {
    return message.warning('请先选择一条数据');
  }
  const id = rows[0].id;
  Modal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await deleteShipmentApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const state = reactive<{
  detailVisible: boolean;
  loading: boolean;
  visible: boolean;
}>({
  visible: false,
  detailVisible: false,
  loading: false,
});
const signForm = ref<SignShipment>({
  shipmentId: 0,
  signDate: '',
  signerName: '',
});

// 签收
const confirmAccept = (row: ShipmentBaseInfo) => {
  signForm.value = {
    shipmentId: row.id as number,
    signDate: '',
    signerName: '',
  };
  state.visible = true;
};
const signDetail = async (row: ShipmentBaseInfo) => {
  signForm.value = await getSignDetailApi({ id: row.id as number });
  state.detailVisible = true;
};
const signFormRef = ref();
const sign = async () => {
  await signFormRef.value.validate();
  try {
    state.loading = true;
    await signShipmentApi(signForm.value);
    message.success($t('base.resSuccess'));
    state.visible = false;
    await gridApi.reload();
  } finally {
    state.loading = false;
  }
};
const clear = () => {
  signForm.value = {
    shipmentId: 0,
    signDate: '',
    signerName: '',
  };
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button :icon="h(PlusOutlined)" class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
        <Button class="mr-2" type="primary" danger @click="del">
          {{ $t('base.del') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <!--<TypographyLink @click="edit(row)">-->
          <!--  {{ $t('base.edit') }}-->
          <!--</TypographyLink>-->
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink v-if="row.signFlag === 0" @click="confirmAccept(row)">
            {{ $t('base.confirmAccept') }}
          </TypographyLink>
          <TypographyLink v-if="row.signFlag === 1" @click="signDetail(row)"> 签收详情 </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetailForm" />
    <Modal v-model:visible="state.visible" title="确认收货" @ok="sign" @cancel="clear">
      <Form ref="signFormRef" :model="signForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <FormItem label="收货日期" name="signDate" required>
          <DatePicker v-model:value="signForm.signDate" value-format="x" class="w-full" />
        </FormItem>
        <FormItem label="签收人" name="signerName" required>
          <Input v-model:value="signForm.signerName" />
        </FormItem>
        <FormItem label="上传文件" name="receiptFileId">
          <BaseFilePickList v-model="signForm.receiptFileId" />
        </FormItem>
        <FormItem label="备注" name="remarks">
          <Textarea v-model:value="signForm.remarks" />
        </FormItem>
      </Form>
    </Modal>
    <Modal v-model:visible="state.detailVisible" title="签收详情" @cancel="clear">
      <Descriptions :column="1" class="mt-4">
        <DescriptionsItem label="签收日期">{{ signForm.signDate }}</DescriptionsItem>
        <DescriptionsItem label="签收人">{{ signForm.signerName }}</DescriptionsItem>
        <DescriptionsItem label="签收文件">
          <BaseFilePickList v-model="signForm.receiptFileId" :edit-mode="false" />
        </DescriptionsItem>
        <DescriptionsItem label="备注">{{ signForm.remarks }}</DescriptionsItem>
      </Descriptions>
      <template #footer> </template>
    </Modal>
  </Page>
</template>

<style></style>
