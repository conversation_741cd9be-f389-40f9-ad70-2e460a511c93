<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import type { SelectValue } from 'ant-design-vue/es/select';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ShipmentBaseInfo } from '#/api';

import { computed, nextTick, ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Textarea,
} from 'ant-design-vue';

import { BaseAttachmentList, BaseRegionPicker } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addShipmentApi, detailShipmentApi, editShipmentApi, getCompanyApi, projectManageListApi } from '#/api';
import { ProjectSelector } from '#/components';
import ItemsPick from '#/components/business/ItemsPick.vue';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();

interface SelectOption {
  projectName?: string;
  id?: number | string;
  projectCode?: string;
  executorCompanyName?: string;
}

const baseGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  showFooter: true,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
} as VxeTableGridOptions;
const deliveryNumberTitle = computed(() => {
  switch (detailForm.value.transportMethod) {
    case 'ROAD': {
      return '车辆号';
    }
    case 'SEA': {
      return '船舶号';
    }
    default: {
      return '物流号';
    }
  }
});
const belongProjectOptions = ref<SelectOption[]>([]);

// 默认数据
const defaultForm: Partial<ShipmentBaseInfo> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  shipmentCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  shipmentType: undefined,
  transportMethod: undefined,
  carrierCompanyCode: undefined,
  carrierCompanyName: undefined,
  consigneeCompanyCode: undefined,
  consigneeCompanyName: undefined,
  billingCompanyCode: undefined,
  billingCompanyName: undefined,
  shipmentDate: undefined,
  status: undefined,
  totalShipmentCost: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  plannedDeliveryDate: undefined,
  receiptDistrict: undefined,
  receiptDetailAddress: undefined,
  receiptProvince: undefined,
  receiptCity: undefined,
  remarks: undefined,
  shipmentDeliveryList: [],
  shipmentItemList: [],
  shipmentSourceRelList: [],
  attachmentList: [],
  documentType: undefined,
};
let detailForm = ref<Partial<ShipmentBaseInfo>>(cloneDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  shipmentType: [{ required: true, message: '请选择运输类型', trigger: 'change' }],
  transportMethod: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
  projectId: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  sourceDocumentType: [{ required: true, message: '请选择源单类型', trigger: 'change' }],
  shipmentSourceRelList: [{ required: true, message: '请选择关联单据', trigger: 'change' }],
  consigneeCompanyCode: [{ required: true, message: '请选择收货企业', trigger: 'change' }],
};

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const title = computed(() => {
  return detailForm.value.id ? '编辑发货单' : '新增发货单';
});

const init = async (data: any) => {
  const res = await projectManageListApi({ projectName: '' });
  belongProjectOptions.value = res.map((item) => ({
    projectName: item.projectName,
    id: item.id,
  }));
  await getCompanyList();
  if (data.id) {
    const res = await detailShipmentApi(data.id);
    Object.assign(detailForm.value, res);

    // 强制刷新表格数据
    setTimeout(() => {
      gridApi.grid.loadData(detailForm.value.shipmentDeliveryList || []);
      gridApiGoods.grid.loadData(detailForm.value.shipmentItemList || []);
    }, 0);
  } else {
    detailForm.value = cloneDeep(defaultForm);
    // 清空表格数据
    setTimeout(() => {
      gridApi.grid.loadData([]);
      gridApiGoods.grid.loadData([]);
    }, 0);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);

    // 手动同步表格数据
    const deliveryTableData = gridApi.grid.getTableData();
    const goodsTableData = gridApiGoods.grid.getTableData();

    // 更新 detailForm 中的数据
    detailForm.value.shipmentDeliveryList = [...deliveryTableData.visibleData];
    detailForm.value.shipmentItemList = [...goodsTableData.visibleData];

    const formData = {
      ...detailForm.value,
      shipmentDeliveryList: detailForm.value.shipmentDeliveryList || [],
      shipmentItemList: detailForm.value.shipmentItemList || [],
      shipmentSourceRelList: detailForm.value.shipmentSourceRelList || [],
    } as ShipmentBaseInfo;

    const res = detailForm.value.id ? await editShipmentApi(formData) : await addShipmentApi(formData);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
    close();
  } catch (error) {
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const grid: VxeTableGridOptions = {
  ...baseGridOptions,
  data: detailForm.value.shipmentDeliveryList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'deliveryType',
      title: '运输类型',
      slots: { default: 'deliveryType' },
      minWidth: '150px',
    },
    {
      field: 'deliveryNumber',
      title: '车辆号/船舶号/物流号',
      slots: { default: 'deliveryNumber' },
      minWidth: '160px',
    },
    {
      field: 'contactName',
      title: '联系人姓名',
      slots: { default: 'contactName' },
      minWidth: '150px',
    },
    {
      field: 'contactPhone',
      title: '联系人电话',
      slots: { default: 'contactPhone' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', slots: { default: 'remarks' }, minWidth: '200px' },
  ],
};

const gridGoods: VxeTableGridOptions = {
  ...baseGridOptions,
  data: detailForm.value.shipmentItemList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      slots: { default: 'productName' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { default: 'productAlias' },
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { default: 'productCode' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { default: 'specifications' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '单位',
      slots: { default: 'measureUnit' },
      minWidth: '150px',
    },
    {
      field: 'quantity',
      title: '数量',
      minWidth: '150px',
    },
    {
      field: 'shippedQuantity',
      title: '本次发运重量',
      slots: { default: 'shippedQuantity' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentCode',
      title: '源单据编号',
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单据商品行号',
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', slots: { default: 'remarks' }, minWidth: '200px' },
  ],
};

const handleProjectChange = (_id: number, option: any) => {
  detailForm.value.projectCode = option.projectCode;
  detailForm.value.projectName = option.projectName;
  detailForm.value.executorCompanyName = option.executorCompanyName;
  gridApiGoods.grid.loadData([]);
};

const handleDocumentTypeChange = async () => {
  detailForm.value.shipmentSourceRelList = [];
  gridApiGoods.grid.loadData([]);
};

// 新增行
const addLocationRow = async (gridApi: any) => {
  const newRecord = {
    id: detailForm.value.shipmentDeliveryList!.length,
    version: undefined,
    shipmentId: undefined,
    deliveryType: undefined,
    deliveryNumber: undefined,
    contactName: undefined,
    contactPhone: undefined,
    remarks: undefined,
  };
  const $grid = gridApi.grid;
  if ($grid) {
    await $grid.insertAt(newRecord, -1);
  }
};

// 删除行
const removeLocationRow = async (gridApi: any) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectedRows = $grid.getCheckboxRecords();
    if (selectedRows.length > 0) {
      $grid.remove(selectedRows);
      message.success('删除成功');
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};
const ItemsPickRef = ref();
const resetGoods = async () => {
  if (!detailForm.value.sourceDocumentType) {
    return message.warning('请先选择源单类型');
  }
  if (!detailForm.value.projectId) {
    return message.warning('请先选择项目');
  }
  const newGoods = await ItemsPickRef.value.pick({
    sourceDocumentType: detailForm.value.sourceDocumentType,
    projectId: detailForm.value.projectId,
  });
  if (!newGoods || newGoods.length === 0) {
    return;
  }
  const currentGoods = gridApiGoods.grid ? gridApiGoods.grid.getTableData().visibleData : [];

  const goodsToInsert = [];
  for (const good of newGoods) {
    const exists = currentGoods.some(
      (item) =>
        item.sourceDocumentItemNumber === good.sourceDocumentItemNumber &&
        item.sourceDocumentId === good.sourceDocumentId,
    );
    if (!exists) {
      good.shippedQuantity = good.quantity;
      goodsToInsert.push(good);
    }
  }
  if (goodsToInsert.length > 0) {
    await gridApiGoods.grid.insertAt(goodsToInsert, -1);
  } else {
    message.warning('没有可插入的新商品，请勿重复选择');
  }
};

const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};

// 注册表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: grid,
});

const [GridGoods, gridApiGoods] = useVbenVxeGrid({
  gridOptions: gridGoods,
});

const handleConsigneeCompanyChange = (_value: SelectValue, option?: any) => {
  detailForm.value.consigneeCompanyName = option?.companyName;
};
const handleBillingCompanyChange = (_value: SelectValue, option?: any) => {
  detailForm.value.billingCompanyName = option?.companyName;
};
const handleCarrierCompanyChange = (_value: SelectValue, option?: any) => {
  detailForm.value.carrierCompanyName = option?.companyName;
};

// 监听 shipmentDeliveryList 变化并更新表格
watch(
  () => detailForm.value.shipmentDeliveryList,
  (newVal) => {
    if (gridApi.grid) {
      nextTick(() => {
        gridApi.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);

// 监听 shipmentItemList 变化并更新表格
watch(
  () => detailForm.value.shipmentItemList,
  (newVal) => {
    if (gridApiGoods.grid) {
      nextTick(() => {
        gridApiGoods.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    show-ok-btn
    :title="title"
    ok-text="提交"
    @register="registerPopup"
    @ok="save"
    @close="close"
  >
    <div :class="BASE_PAGE_CLASS_NAME">
      <Form
        ref="formRef"
        :colon="false"
        :model="detailForm"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        class="px-8"
      >
        <!-- 基本信息 -->
        <Row class="mt-5">
          <!-- 发货单编号 -->
          <Col v-bind="colSpan">
            <FormItem label="发货单编号" name="shipmentCode">
              <Input v-model:value="detailForm.shipmentCode" disabled />
            </FormItem>
          </Col>
          <!-- 运输企业 -->
          <Col v-bind="colSpan">
            <FormItem label="运输企业" name="carrierCompanyCode">
              <Select
                v-model:value="detailForm.carrierCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
                @change="handleCarrierCompanyChange"
              />
            </FormItem>
          </Col>
          <!-- 所属项目名称 -->
          <Col v-bind="colSpan">
            <FormItem label="项目名称" name="projectId">
              <ProjectSelector v-model="detailForm.projectId" @change="handleProjectChange" />
            </FormItem>
          </Col>
          <!-- 关联单据类型 -->
          <Col v-bind="colSpan">
            <FormItem label="源单类型" name="sourceDocumentType">
              <Select
                v-model:value="detailForm.sourceDocumentType"
                :options="getDictList('DOCUMENT_TYPE')"
                @change="handleDocumentTypeChange"
              />
            </FormItem>
          </Col>
          <!-- 运输类型 -->
          <Col v-bind="colSpan">
            <FormItem label="发运类型" name="shipmentType">
              <Select v-model:value="detailForm.shipmentType" :options="getDictList('TRANSPORT_TYPE')" />
            </FormItem>
          </Col>
          <!-- 运输方式 -->
          <Col v-bind="colSpan">
            <FormItem label="运输方式" name="transportMethod">
              <Select v-model:value="detailForm.transportMethod" :options="getDictList('TRANSPORT_MODE')" />
            </FormItem>
          </Col>
          <!-- 收货企业 -->
          <Col v-bind="colSpan">
            <FormItem label="收货企业" name="consigneeCompanyCode">
              <Select
                v-if="companyOptions && companyOptions.length > 0"
                v-model:value="detailForm.consigneeCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
                @change="handleConsigneeCompanyChange"
              />
            </FormItem>
          </Col>
          <!-- 结算公司	 -->
          <Col v-bind="colSpan">
            <FormItem label="结算企业" name="billingCompanyCode">
              <Select
                v-model:value="detailForm.billingCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
                @change="handleBillingCompanyChange"
              />
            </FormItem>
          </Col>
          <!-- 发运日期 -->
          <Col v-bind="colSpan">
            <FormItem label="发运日期" name="shipmentDate">
              <DatePicker
                v-model:value="detailForm.shipmentDate"
                value-format="YYYY-MM-DD hh:mm:ss"
                format="YYYY-MM-DD"
                class="w-full"
              />
            </FormItem>
          </Col>
          <!-- 预计收货日期 -->
          <Col v-bind="colSpan">
            <FormItem label="预计收货日期" name="plannedDeliveryDate">
              <DatePicker
                v-model:value="detailForm.plannedDeliveryDate"
                value-format="YYYY-MM-DD hh:mm:ss"
                format="YYYY-MM-DD"
                class="w-full"
              />
            </FormItem>
          </Col>
          <!-- 运输费用 -->
          <Col v-bind="colSpan">
            <FormItem label="运输费用" name="totalShipmentCost">
              <InputNumber v-model:value="detailForm.totalShipmentCost" :controls="false" class="w-full">
                <template #addonAfter> 元 </template>
              </InputNumber>
            </FormItem>
          </Col>
          <!-- 收货地址 -->
          <Col v-bind="colSpan">
            <Row :gutter="12">
              <Col :span="12">
                <FormItem
                  label="收货地址"
                  name="receiptDetailAddress"
                  v-bind="{ labelCol: { span: 8 }, wrapperCol: { span: 16 } }"
                >
                  <BaseRegionPicker
                    v-model:province="detailForm.receiptProvince"
                    v-model:city="detailForm.receiptCity"
                    v-model:district="detailForm.receiptDistrict"
                    :disabled="!!detailForm.id"
                  />
                </FormItem>
              </Col>
              <Col :span="12">
                <FormItem name="receiptDetailAddress" v-bind="{ wrapperCol: { span: 24 } }">
                  <Input v-model:value="detailForm.receiptDetailAddress" :disabled="!!detailForm.id" />
                </FormItem>
              </Col>
            </Row>
          </Col>
          <!-- 备注 -->
          <Col :span="24">
            <FormItem label="备注" name="remarks" v-bind="{ labelCol: { span: 2 }, wrapperCol: { span: 22 } }">
              <Textarea v-model:value="detailForm.remarks" :rows="3" />
            </FormItem>
          </Col>
        </Row>

        <!-- 物流运输信息 -->
        <BasicCaption content="交货信息" />
        <div>
          <Grid>
            <template #toolbarTools>
              <Button class="mr-2" type="primary" @click="() => addLocationRow(gridApi)">增行</Button>
              <Button class="mr-2" danger @click="() => removeLocationRow(gridApi)">删行</Button>
            </template>

            <template #deliveryType="{ row }">
              <Select
                :options="getDictList('TRANSPORT_VEHICLE')"
                v-model:value="row.deliveryType"
                placeholder="请选择"
                class="w-full"
              />
            </template>
            <template #deliveryNumberHeader>
              <span>{{ deliveryNumberTitle }}</span>
            </template>
            <template #deliveryNumber="{ row }">
              <Input v-model:value="row.deliveryNumber" placeholder="请输入" />
            </template>

            <template #contactName="{ row }">
              <Input v-model:value="row.contactName" placeholder="请输入" />
            </template>

            <template #contactPhone="{ row }">
              <Input v-model:value="row.contactPhone" placeholder="请输入" />
            </template>

            <template #remarks="{ row }">
              <Input v-model:value="row.remarks" placeholder="请输入" />
            </template>
          </Grid>
        </div>

        <!-- 商品信息 -->
        <BasicCaption content="商品信息" />
        <div>
          <GridGoods>
            <template #toolbarTools>
              <Button class="mr-2" type="primary" @click="() => resetGoods()">选择商品</Button>
              <Button class="mr-2" danger @click="() => removeLocationRow(gridApiGoods)">删行</Button>
            </template>

            <template #productName="{ row }">
              {{ row.productName }}
            </template>

            <template #productAlias="{ row }">
              {{ row.productAlias }}
            </template>

            <template #productCode="{ row }">
              {{ row.productCode }}
            </template>

            <template #specifications="{ row }">
              {{ row.specifications }}
            </template>

            <template #measureUnit="{ row }">
              {{ row.measureUnit }}
            </template>

            <template #brandName="{ row }"> {{ row.brandName }} </template>

            <template #originName="{ row }"> {{ row.originName }} </template>

            <template #shippedQuantity="{ row }">
              <Input v-model:value="row.shippedQuantity" placeholder="本次发运重量" />
            </template>

            <template #sourceDocumentItemNumber="{ row }">
              {{ row.sourceDocumentItemNumber }}
            </template>

            <template #sourceDocumentName="{ row }">
              {{ row.sourceDocumentName }}
            </template>

            <template #sourceDocumentCode="{ row }">
              {{ row.sourceDocumentCode }}
            </template>

            <template #remarks="{ row }">
              <Input v-model:value="row.remarks" placeholder="备注" />
            </template>
          </GridGoods>
        </div>

        <!-- 附件信息 -->
        <BaseAttachmentList
          v-model="detailForm.attachmentList"
          :business-id="detailForm.id"
          business-type="SCM_SHIPMENT"
          edit-mode
        />
      </Form>
    </div>
    <ItemsPick ref="ItemsPickRef" />
  </BasicPopup>
</template>

<style></style>
