import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * ContractTemplateRequest，模板表
 */
export interface ContractTemplateInfo {
  // 分类ID
  categoryId?: number;
  // 文件Id
  fileId?: number;
  // 主键
  id?: number;
  // 模板状态
  state?: number;
  // 模板Id
  templateId?: string;
  // 模板名称
  templateName?: string;
  // 模板类型
  templateType?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}
/**
 * 合同信息
 */
export interface ContractInfo {
  // 业务编码
  businessCode?: string;
  // 业务ID
  businessId?: number;
  // 业务类型
  businessType?: string;
  // 作废原因
  cancelReason?: string;
  // 最终签署时间
  completedDate?: Date | number;
  // 合同编号
  contractCode?: string;
  // 合同名称
  contractName?: string;
  // 创建类型：1:直接创建、2:模板创建
  createType?: string;
  // 签署截止时间
  deadlineDate?: Date | number;
  // 操作结果
  description?: string;
  // 签署合同路径（新）
  fileCompleteId?: string;
  // 上传合同路径（原）
  fileId?: number;
  // ID
  id?: number;
  // 发起签署时间
  initiatedDate?: Date | number;
  // 项目ID
  projectId?: number;
  // 签署方
  signDetailList?: ContractSignDetailRequest[];
  // 签署方式：1:线上、2:线下
  signMethod?: string;
  // 是否顺序签署
  signOrder?: number;
  // 合同状态，1:未提交，2:审批中，3:审批驳回，4:审核拒绝，5:未签署，6:待签署，7:部分签署，8:已签署，9:超期失效，10:拒签，11:已作废
  status?: string;
  // 模板主键
  templateId?: number;
  [property: string]: any;
}
/**
 * ContractSignDetailRequest，合同签署方参数
 */
export interface ContractSignDetailRequest {
  // 签署人账号
  signatoryAccount?: string;
  // 签署时间
  signatoryDate?: Date;
  // 签署经办人
  signatoryName?: string;
  // 签署方名称（机构）
  signatoryOrg?: string;
  // 签署参数名称（目前固定）：甲方、乙方、丙方、丁方
  signatoryParam?: string;
  // 签署方类型，0 - 个人，1 - 机构，2 - 法定代表人
  signerType?: string;
  // 签署顺序
  signOrder?: number;
  // 签署短链接（180天有效）
  signShortUrl?: string;
  // 签署结果： 未签署、待签署、超期失效、拒签
  signStatus?: string;
  // 签署长链接（永久有效）
  signUrl?: string;
  [property: string]: any;
}

export async function getContractTemplateListApi() {
  return requestClient.get<ContractTemplateInfo[]>('/base/contract/template/list');
}
export async function getContractSignInfoApi(params: { id: number }) {
  return requestClient.get<ContractInfo>('/base/contract/info', { params });
}
export async function getContractTemplateInfoApi(params: { id: number }) {
  return requestClient.get('/base/contract/template/info', { params });
}
export async function saveContractSignApi(data: ContractInfo) {
  return requestClient.post('/base/contract/save', data);
}
export async function getContractSignPageListApi(params: PageListParams) {
  return requestClient.get('/base/contract/pageList', { params });
}
export async function getBusinessContractListApi(params: { businessId: number; businessType: string }) {
  return requestClient.get('/base/contract/business/list', { params });
}
export async function getTempContractInfoApi(params: { id: number }) {
  return requestClient.get('/base/contract/business/temp/info', { params });
}
export async function saveTempContractSignApi(data: ContractInfo) {
  return requestClient.post('/base/contract/business/temp/save', data);
}
export async function saveBusinessContractSignApi(data: ContractInfo) {
  return requestClient.post('/base/contract/business/save', data);
}
export async function saveBatchTempContractSignApi(data: number[]) {
  return requestClient.post('/base/contract/business/temp/batch-save-by-upload', data);
}
export async function saveBatchBusinessContractSignApi(data: number[]) {
  return requestClient.post('/base/contract/business/batch-save-by-upload', data);
}
export async function getContractClassifyListApi() {
  return requestClient.get('/base/contract/category/list');
}
export async function delContractBusinessApi(params: { id: number }) {
  return requestClient.post('/base/contract/business/delete', {}, { params });
}
export async function delContractTemplateApi(params: { id: number }) {
  return requestClient.post('/base/contract/business/temp/delete', {}, { params });
}
