import type { RequestClientConfig } from '@vben/request';
import type { PageListParams } from '@vben/types';

import type { OrderCompanyInfo, OrderInfo } from './purchase';

import { requestClient } from '#/api/request';

export interface QueryGoodsRequestSales extends OrderCompanyInfo {
  projectId?: number;
  orderList?: string[];
  businessStructure?: string;
  isSnManaged?: boolean;
  warehouseId?: number;
  sourceDocumentType?: string;
  customerCompanyCode?: string;
  executorCompanyCode?: string;
}

/**
 * SplitSalesOrderRequest
 */
export interface SplitSalesOrderInfo {
  // 采购订单列表
  purchaseOrderList?: PurchaseOrderRequest[];
  // 关联的销售订单ID
  salesOrderId?: number;
  [property: string]: any;
}

/**
 * PurchaseOrderRequest，采购订单列表
 */
export interface PurchaseOrderRequest {
  // 采购订单商品信息列表
  itemList?: PurchaseOrderItemRequest[];
  // 采购订单编码
  purchaseOrderCode?: string;
  // 采购订单名称
  purchaseOrderName?: string;
  // 供应商编码
  supplierCompanyCode?: string;
  // 供应商名称
  supplierCompanyName?: string;
  [property: string]: any;
}

/**
 * PurchaseOrderItemRequest，采购订单商品信息
 */
export interface PurchaseOrderItemRequest {
  // 含税单价
  priceWithTax?: number;
  // 源商品行Id（对应销售订单中的商品行）
  sourceItemId?: number;
  [property: string]: any;
}

export async function getSalesListApi(params: PageListParams) {
  return requestClient.get('/scm/order/sales/page', { params });
}
export async function addSalesListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/sales/add', data);
}
export async function editSalesListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/sales/update', data);
}
export async function infoSalesListApi(params: OrderInfo) {
  return requestClient.get<OrderInfo>('/scm/order/sales/detail', { params });
}
export async function getSalesOrderItemListApi(params: {
  includeNumbers?: number[];
  orderId: number;
  type: 'ALL' | 'UNSPLIT';
}) {
  return requestClient.get('/scm/order/sales/itemList', { params });
}
export async function getSalesOrderItemPageApi(params: PageListParams & { keyword?: string }) {
  return requestClient.get('/scm/order/sales/itemPage', { params });
}
export async function changeSalesApi(params: OrderInfo) {
  // CANCEL：作废   DRAFT：变更
  return requestClient.post<OrderInfo>('/scm/order/sales/change', params);
}
export async function delSalesApi(id: number) {
  return requestClient.post(`/scm/order/sales/delete?id=${id}`);
}
export async function importSalesApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/scm/order/sales/upload', data, config);
}
export async function downloadSalesTemplateApi() {
  return requestClient.downloadAndSave('/scm/order/sales/download');
}
// 出库商品查询
export async function getInventoryStockApi(params: QueryGoodsRequestSales) {
  return requestClient.post('/scm/inventory/stock/queryItemList', { params });
}
export async function splitSalesOrderApi(data: SplitSalesOrderInfo) {
  return requestClient.post('/scm/order/sales/split-to-purchase-order', data);
}
export async function checkSalesOrderCodeApi(params: { salesOrderCode: string }) {
  return requestClient.get('/scm/order/sales/check-order-code', { params, responseReturn: 'body' });
}
