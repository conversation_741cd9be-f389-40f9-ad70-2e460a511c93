import type { PageListParams } from '@vben/types';

import type { OrderInfo } from './purchase';

import { requestClient } from '#/api/request';

export async function getSalesReturnListApi(params: PageListParams) {
  return requestClient.get<OrderInfo[]>('/scm/order/sales/return/page', { params });
}
export async function addSalesReturnListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/sales/return/add', data);
}
export async function editSalesReturnListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/sales/return/update', data);
}
export async function infoSalesReturnListApi(params: OrderInfo) {
  return requestClient.get<OrderInfo>('/scm/order/sales/return/detail', { params });
}
export async function changeSalesReturnApi(params: OrderInfo) {
  // CANCEL：作废   DRAFT：变更
  return requestClient.post<OrderInfo>('/scm/order/sales/return/change', params);
}
export async function delSalesReturnApi(id: number) {
  return requestClient.post(`/scm/order/sales/return/delete?id=${id}`);
}
export async function getSalesReturnOrderItemPageApi(params: PageListParams & { keyword?: string }) {
  return requestClient.get('/scm/order/sales/return/itemPage', { params });
}
