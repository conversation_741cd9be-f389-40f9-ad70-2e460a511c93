import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 表示数据库中 tinyint(1) 类型的布尔标志。
 * 0 代表 false/否/无, 1 代表 true/是/有。
 */
type BoolNumber = 0 | 1;

/**
 * 运营检查
 */
export interface InspectionInfo {
  /** 审批状态 */
  approvalStatus?: string;
  /** 资产分类 */
  assetClassification?: string;
  /** 核心/担保企业-是否新增银行融资 (1=是, 0=否) */
  cgBankLoanAdded?: BoolNumber;
  /** 核心/担保企业-新增银行融资具体情况 */
  cgBankLoanDetails?: string;
  /** 核心企业-产能产量有无变化 (1=有, 0=无) */
  cgCapacityChanged?: BoolNumber;
  /** 核心企业-产能产量变化具体情况 */
  cgCapacityDetails?: string;
  /** 核心/担保企业-注册资本有无变化 (1=有, 0=无) */
  cgCapitalChanged?: BoolNumber;
  /** 核心/担保企业-注册资本变化具体情况 */
  cgCapitalDetails?: string;
  /** 核心/担保企业-银行征信有无变化 (1=有, 0=无) */
  cgCreditChanged?: BoolNumber;
  /** 核心/担保企业-银行征信具体情况 */
  cgCreditDetails?: string;
  /** 核心企业-是否新增下游客户 (1=是, 0=否) */
  cgCustomerAdded?: BoolNumber;
  /** 核心企业-新增下游客户具体情况 */
  cgCustomerDetails?: string;
  /** 核心企业-情况描述 */
  cgEnterpriseDetails?: string;
  /** 核心企业-生产环境有无变化 (1=有, 0=无) */
  cgEnvironmentChanged?: BoolNumber;
  /** 核心企业-生产环境变化具体情况 */
  cgEnvironmentDetails?: string;
  /** 核心/担保企业-股权结构有无变化 (1=有, 0=无) */
  cgEquityChanged?: BoolNumber;
  /** 核心/担保企业-股权结构变化具体情况 */
  cgEquityDetails?: string;
  /** 核心/担保企业-是否新增股权融资 (1=是, 0=否) */
  cgEquityLoanAdded?: BoolNumber;
  /** 核心/担保企业-新增股权融资具体情况 */
  cgEquityLoanDetails?: string;
  /** 核心/担保企业-高管人员有无变化 (1=有, 0=无) */
  cgExecChanged?: BoolNumber;
  /** 核心/担保企业-高管变化具体情况 */
  cgExecDetails?: string;
  /** 核心财务数据 (JSON数组) */
  cgFinancialsData?: string;
  /** 核心财务数据 (JSON数组) */
  cgFinancialsDataJson?: { [key: string]: { [key: string]: any } }[];
  /** 核心/担保企业-是否新增担保 (1=是, 0=否) */
  cgGuaranteeAdded?: BoolNumber;
  /** 核心/担保企业-新增担保具体情况 */
  cgGuaranteeDetails?: string;
  /** 核心/担保企业-司法纠纷有无变化 (1=有, 0=无) */
  cgLegalChanged?: BoolNumber;
  /** 核心/担保企业-司法纠纷具体情况 */
  cgLegalDetails?: string;
  /** 核心/担保企业-是否存在负面舆情 (1=是, 0=否) */
  cgNegativeNews?: BoolNumber;
  /** 核心/担保企业-负面舆情具体情况 */
  cgNegativeNewsDetails?: string;
  /** 核心/担保企业-是否新增非标融资 (1=是, 0=否) */
  cgNonStdLoanAdded?: BoolNumber;
  /** 核心/担保企业-新增非标融资具体情况 */
  cgNonStdLoanDetails?: string;
  /** 核心/担保企业-行政处罚有无变化 (1=有, 0=无) */
  cgPenaltyChanged?: BoolNumber;
  /** 核心/担保企业-行政处罚具体情况 */
  cgPenaltyDetails?: string;
  /** 核心企业-核心工艺有无变化 (1=有, 0=无) */
  cgProcessChanged?: BoolNumber;
  /** 核心企业-核心工艺变化具体情况 */
  cgProcessDetails?: string;
  /** 核心企业-主要产品有无变化 (1=有, 0=无) */
  cgProductChanged?: BoolNumber;
  /** 核心企业-主要产品变化具体情况 */
  cgProductDetails?: string;
  /** 核心/担保企业-经营范围有无变化 (1=有, 0=无) */
  cgScopeChanged?: BoolNumber;
  /** 核心/担保企业-经营范围变化具体情况 */
  cgScopeDetails?: string;
  /** 核心企业-是否新增上游供应商 (1=是, 0=否) */
  cgSupplierAdded?: BoolNumber;
  /** 核心企业-新增上游供应商具体情况 */
  cgSupplierDetails?: string;
  /** 抵质押物存续情况 */
  collateralStatus?: string;
  /** 检查结论 */
  conclusion?: string;
  /** 下游-是否按时回款 (1=是, 0=否) */
  contractCustomerPaymentOntime?: BoolNumber;
  /** 下游-是否按时回款具体情况 */
  contractCustomerPaymentOntimeDetails?: string;
  /** 下游-是否临时变更采购计划 (1=是, 0=否) */
  contractCustomerPlanChanged?: BoolNumber;
  /** 下游-临时变更采购计划具体情况 */
  contractCustomerPlanChangedDetails?: string;
  /** 下游-是否存在退换货 (1=是, 0=否) */
  contractCustomerReturn?: BoolNumber;
  /** 下游-退换货具体情况 */
  contractCustomerReturnDetails?: string;
  /** 合同履约具体情况描述 */
  contractPerfDetails?: string;
  /** 上游-是否按期到货 (1=是, 0=否) */
  contractSupplierDelivArrivalOntime?: BoolNumber;
  /** 上游-是否按时到货具体情况 */
  contractSupplierDelivArrivalOntimeDetails?: string;
  /** 上游-发货是否及时 (1=是, 0=否) */
  contractSupplierDelivOntime?: BoolNumber;
  /** 上游-发货是否及时具体情况 */
  contractSupplierDelivOntimeDetails?: string;
  /** 上游-是否超发少发 (1=是, 0=否) */
  contractSupplierDelivQtyIssue?: BoolNumber;
  /** 上游-超发少发具体情况 */
  contractSupplierDelivQtyIssueDetails?: string;
  /** 上游-是否错发货物 (1=是, 0=否) */
  contractSupplierDelivWrongItem?: BoolNumber;
  /** 上游-错发货物具体情况 */
  contractSupplierDelivWrongItemDetails?: string;
  /** 上游-是否存在开票差错 (1=是, 0=否) */
  contractSupplierInvoiceError?: BoolNumber;
  /** 上游-开票差错具体情况 */
  contractSupplierInvoiceErrorDetails?: string;
  /** 上游-开票是否及时 (1=是, 0=否) */
  contractSupplierInvoiceOntime?: BoolNumber;
  /** 上游-开票是否及时具体情况 */
  contractSupplierInvoiceOntimeDetails?: string;
  /** 上游-是否定期对账结算 (1=是, 0=否) */
  contractSupplierReconRegular?: BoolNumber;
  /** 上游-定期对账结算具体情况 */
  contractSupplierReconRegularDetails?: string;
  /** 核心企业名称 */
  coreEnterpriseName?: string;
  /** 增信措施落实情况 */
  creditEnhancementImplementation?: string;
  /** 增信措施 */
  creditEnhancementMeasures?: string;
  /** 合作额度 */
  creditLimit?: number | string;
  /** 累计投放金额 */
  cumulativeLendingAmount?: number;
  /** 下游客户情况 */
  customerDataDetails?: string;
  /** 库供下游客户情况(JSON数组) */
  customerDataDetailsJson?: { [key: string]: { [key: string]: any } }[];
  /** 底稿附件 */
  draftAttachment?: string;
  /** 能耗数据 (JSON数组) */
  energyConsumptionData?: string;
  /** 能耗数据 (JSON数组) */
  energyConsumptionDataJson?: { [key: string]: { [key: string]: any } };
  /** 担保企业名称 */
  guarantorEnterpriseName?: string;
  /** 主键 */
  id?: number;
  /** 检查时间 */
  inspectionDate?: number;
  /** 检查方式 */
  inspectionMethod?: string;
  /** 检查人员 */
  inspectionOfficers?: string;
  /** 运营人员 */
  operationsOfficer?: string;
  /** 其他情况 */
  otherInfoSummary?: string;
  /** 存量额度 */
  outstandingBalance?: number;
  /** 合作企业-是否新增银行融资 (1=是, 0=否) */
  partnerBankLoanAdded?: BoolNumber;
  /** 合作企业-新增银行融资具体情况 */
  partnerBankLoanDetails?: string;
  /** 合作企业-注册资本有无变化 (1=有, 0=无) */
  partnerCapitalChanged?: BoolNumber;
  /** 合作企业-注册资本变化具体情况 */
  partnerCapitalDetails?: string;
  /** 合作企业-银行征信有无变化 (1=有, 0=无) */
  partnerCreditChanged?: BoolNumber;
  /** 合作企业-银行征信具体情况 */
  partnerCreditDetails?: string;
  /** 合作企业名称 */
  partnerEnterpriseName?: string;
  /** 合作企业-股权结构有无变化 (1=有, 0=无) */
  partnerEquityChanged?: BoolNumber;
  /** 合作企业-股权结构变化具体情况 */
  partnerEquityDetails?: string;
  /** 合作企业-是否新增股权融资 (1=是, 0=否) */
  partnerEquityLoanAdded?: BoolNumber;
  /** 合作企业-新增股权融资具体情况 */
  partnerEquityLoanDetails?: string;
  /** 合作企业-高管人员有无变化 (1=有, 0=无) */
  partnerExecChanged?: BoolNumber;
  /** 合作企业-高管变化具体情况 */
  partnerExecDetails?: string;
  /** 合作财务数据 (JSON数组) */
  partnerFinancialsData?: string;
  /** 合作财务数据 (JSON数组) */
  partnerFinancialsDataJson?: { [key: string]: { [key: string]: any } }[];
  /** 合作企业-是否新增担保 (1=是, 0=否) */
  partnerGuaranteeAdded?: BoolNumber;
  /** 合作企业-新增担保具体情况 */
  partnerGuaranteeDetails?: string;
  /** 合作企业-司法纠纷有无变化 (1=有, 0=无) */
  partnerLegalChanged?: BoolNumber;
  /** 合作企业-司法纠纷具体情况 */
  partnerLegalDetails?: string;
  /** 合作企业-是否存在负面舆情 (1=是, 0=否) */
  partnerNegativeNews?: BoolNumber;
  /** 合作企业-负面舆情具体情况 */
  partnerNegativeNewsDetails?: string;
  /** 合作企业-是否新增非标融资 (1=是, 0=否) */
  partnerNonStdLoanAdded?: BoolNumber;
  /** 合作企业-新增非标融资具体情况 */
  partnerNonStdLoanDetails?: string;
  /** 合作企业-行政处罚有无变化 (1=有, 0=无) */
  partnerPenaltyChanged?: BoolNumber;
  /** 合作企业-行政处罚具体情况 */
  partnerPenaltyDetails?: string;
  /** 合作企业-经营范围有无变化 (1=有, 0=无) */
  partnerScopeChanged?: BoolNumber;
  /** 合作企业-经营范围变化具体情况 */
  partnerScopeDetails?: string;
  /** 账期 (天) */
  paymentTerm?: number | string;
  /** 提货账期 (天, 库供分销专用) */
  pickupTerm?: number;
  /** 项目Id */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
  /** 项目编号 */
  projectNumber?: string;
  /** 项目类型 */
  projectType?: string;
  /** 采购情况描述 */
  purchaseDetails?: string;
  /** 采购是否波动 (1=是, 0=否) */
  purchaseFluctuate?: BoolNumber;
  /** 采购销售数据 (JSON数组) */
  purchaseSalesData?: string;
  /** 采购销售数据 (JSON数组) */
  purchaseSalesDataJson?: { [key: string]: { [key: string]: any } };
  /** 报告编号 */
  reportCode?: string;
  /** 报告日期 */
  reportDate?: number;
  /** 报告名称 */
  reportName?: string;
  /** 报告类型 */
  reportType?: string;
  /** 复合人员 */
  reviewOfficer?: string;
  /** 服务费率 */
  serviceFeeRate?: number | string;
  /** 业务状态 */
  status?: string;
  /** 库供分销合作方数据 (JSON数组) */
  supplierDataDetails?: string;
  /** 库供分销合作方数据 (JSON数组) */
  wdPartnersDataJson?: { [key: string]: { [key: string]: any } };
  /** 库供分销仓库数据 (JSON数组) */
  warehouseDataDetails?: string;
  /** 库供分销仓库数据 (JSON数组) */
  wdWarehousesDataJson?: { [key: string]: { [key: string]: any } };
  [property: string]: any;
}

export async function getInspectionPageApi(params: PageListParams) {
  return requestClient.get('/scm/operation/inspection/page', { params });
}
export async function addIndustryInspectionApi(data: InspectionInfo) {
  return requestClient.post('/scm/operation/inspection/addIndustry', data);
}
export async function addBuildingInspectionApi(data: InspectionInfo) {
  return requestClient.post('/scm/operation/inspection/addBuilding', data);
}
export async function addWarehouseInspectionApi(data: InspectionInfo) {
  return requestClient.post('/scm/operation/inspection/addWarehouse', data);
}
export async function editInspectionApi(data: InspectionInfo) {
  return requestClient.post('/scm/operation/inspection/edit', data);
}
export async function getInspectionDetailApi(id: number) {
  return requestClient.get(`/scm/operation/inspection/detail/${id}`);
}
export async function downloadInspectionApi(id: number) {
  return requestClient.downloadAndSave(`/scm/operation/inspection/download/${id}`);
}
export async function deleteInspectionApi(id: number) {
  return requestClient.post(`/scm/operation/inspection/delete/${id}`);
}
export async function submitInspectionApi(data: InspectionInfo) {
  return requestClient.post('/scm/operation/inspection/submit', data);
}
export async function downloadFinancialReportTemplateApi() {
  return requestClient.downloadAndSave('/scm/operation/inspection/downloadTemplate');
}
export async function importFinancialReportApi(data: any) {
  return requestClient.upload('/scm/operation/inspection/readFile', data);
}
export async function cancelInspectionApi(id: number) {
  return requestClient.post(`/scm/operation/inspection/cancel/${id}`);
}
