import type { PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * ShipmentAddRequest，发运单添加请求
 */
export interface ShipmentBaseInfo {
  // 业务附件
  attachmentList?: number[];
  // 结算公司编码
  billingCompanyCode?: string;
  // 结算公司名称
  billingCompanyName?: string;
  // 运输企业编码
  carrierCompanyCode?: string;
  // 运输企业名称
  carrierCompanyName?: string;
  // 城市
  city?: string;
  // 收货公司编码
  consigneeCompanyCode?: string;
  // 收货公司名称
  consigneeCompanyName?: string;
  // 创建人ID
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 标记删除
  deleteFlag?: boolean;
  // 详细地址
  detailAddress?: string;
  // 区县
  district?: string;
  // 主键
  id?: number;
  // 计划收货时间
  plannedDeliveryDate?: string;
  // 所属项目编码
  projectCode?: string;
  // 所属项目ID
  projectId?: number;
  // 所属项目名称
  projectName?: string;
  // 省份
  province?: string;
  // 收货城市
  receiptCity?: string;
  // 收货详细地址
  receiptDetailAddress?: string;
  // 收货区县
  receiptDistrict?: string;
  // 收货省份
  receiptProvince?: string;
  // 备注
  remarks?: string;
  // 发运单编号 (系统生成)
  shipmentCode?: string;
  // 发运日期
  shipmentDate?: string;
  // 发货明细
  shipmentDeliveryList?: ShipmentDeliveryBO[];
  // 商品信息
  shipmentItemList?: ShipmentItemBO[];
  // 关联源单据信息
  shipmentSourceRelList?: ShipmentSourceRelBO[];
  // 发货类型
  shipmentType?: string;
  // 状态
  status?: string;
  // 运输总费用
  totalShipmentCost?: number;
  // 运输方式
  transportMethod?: string;
  // 修改人ID
  updateBy?: number;
  // 修改时间
  updateTime?: Date;
  // 版本号
  version?: number;
  [property: string]: any;
}

/**
 * ShipmentDeliveryBO，发货明细
 */
export interface ShipmentDeliveryBO {
  // 联系人姓名
  contactName?: string;
  // 联系人电话
  contactPhone?: string;
  // 车辆号/船舶号/物流号
  deliveryNumber?: string;
  // 发货类型
  deliveryType?: string;
  // 主键
  id?: number;
  // 备注
  remarks?: string;
  // 关联的发货单ID
  shipmentId?: number;
  // 版本号
  version?: number;
  [property: string]: any;
}

/**
 * ShipmentItemBO，商品信息
 */
export interface ShipmentItemBO {
  // 牌号/品牌
  brandName?: string;
  // 主键
  id?: number;
  // 计量单位
  measureUnit?: string;
  // 产地/厂家
  originName?: string;
  // 商品别名
  productAlias?: string;
  // 商品编码
  productCode?: string;
  // 商品名称
  productName?: string;
  // 备注
  remarks?: string;
  // 关联的发运单ID
  shipmentId?: number;
  // 本次发运数量/重量
  shippedQuantity?: number;
  // 源单据编号
  sourceDocumentCode?: string;
  // 源单据ID (如采购订单ID)
  sourceDocumentId?: number;
  // 源单据商品行ID(如采购订单行ID)
  sourceDocumentItemNumber?: number;
  // 源单据名称
  sourceDocumentName?: string;
  // 规格型号
  specifications?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}

/**
 * ShipmentSourceRelBO，关联源单据信息
 */
export interface ShipmentSourceRelBO {
  // 主键
  id?: number;
  // 关联的发运单ID
  shipmentId?: number;
  // 源单据编号
  sourceDocumentCode?: string;
  // 源单据ID (如采购订单ID)
  sourceDocumentId?: number;
  // 源单据名称
  sourceDocumentName?: string;
  // 源单据类型 (如: PURCHASE_ORDER, SALES_RETURN)
  sourceDocumentType?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}

// 发货明细
export interface ShipmentDeliveryBO {
  id?: number; // 主键
  version?: number; // 版本号
  shipmentId?: number; // 关联的发货单ID
  deliveryType?: string; // 发货类型
  deliveryNumber?: string; // 车辆号/船舶号/物流号
  contactName?: string; // 联系人姓名
  contactPhone?: string; // 联系人电话
  remarks?: string; // 备注
}

// 商品信息
export interface ShipmentItemBO {
  id?: number; // 主键
  version?: number; // 版本号
  shipmentId?: number; // 关联的发运单ID
  productCode?: string; // 商品编码
  productName?: string; // 商品名称
  productAlias?: string; // 商品别名
  specifications?: string; // 规格型号
  measureUnit?: string; // 计量单位
  brandName?: string; // 牌号/品牌
  originName?: string; // 产地/厂家
  shippedQuantity?: number; // 本次发运数量/重量
  sourceDocumentItemNumber?: number; // 源单据商品行ID(如采购订单行ID)
  sourceDocumentId?: number; // 源单据ID (如采购订单ID)
  sourceDocumentCode?: string; // 源单据编号
  sourceDocumentName?: string; // 源单据名称
  remarks?: string; // 备注
}

// 源单据信息
export interface ShipmentSourceRelBO {
  id?: number; // 主键
  version?: number; // 版本号
  orderName?: string; // 源单据名称
  orderCode?: string; // 源单据编号
  shipmentId?: number; // 关联的发运单ID
  sourceDocumentId?: number; // 源单据ID (如采购订单ID)
  sourceDocumentCode?: string; // 源单据编号
  sourceDocumentName?: string; // 源单据名称
  sourceDocumentType?: string; // 源单据类型 (如: PURCHASE_ORDER, SALES_RETURN)
}

/**
 * ShipmentSignRequest
 */
export interface SignShipment {
  // 业务附件
  attachmentList?: number[];
  // 创建人ID
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 标记删除
  deleteFlag?: boolean;
  // 主键
  id?: number;
  // 签收文件Id
  receiptFileId?: string;
  // 备注
  remarks?: string;
  // 发运单编号
  shipmentId: number;
  // 签收日期
  signDate: string;
  // 签收人姓名
  signerName: string;
  // 修改人ID
  updateBy?: number;
  // 修改时间
  updateTime?: Date;
  // 版本号
  version?: number;
  [property: string]: any;
}

// 发货列表分页查询
export async function getShipmentPageApi(params: PageListParams) {
  return requestClient.get<Pagination<ShipmentBaseInfo>>('/scm/shipment/manage/page', { params });
}

// 新增发运单
export async function addShipmentApi(data: ShipmentBaseInfo) {
  return requestClient.post<ShipmentBaseInfo>('/scm/shipment/manage/add', data);
}

// 编辑发运单
export async function editShipmentApi(data: ShipmentBaseInfo) {
  return requestClient.post<ShipmentBaseInfo>('/scm/shipment/manage/edit', data);
}

// 发货详情查询
export async function detailShipmentApi(id: string) {
  return requestClient.get(`/scm/shipment/manage/detail/${id}`);
}

// 发货信息删除
export async function deleteShipmentApi(id: string) {
  return requestClient.post(`/scm/shipment/manage/delete/${id}`);
}

// 发货信息作废
export async function cancelShipmentApi(id: string) {
  return requestClient.post(`/scm/shipment/manage/cancel/${id}`);
}

// 确认签收
export async function signShipmentApi(data: SignShipment) {
  return requestClient.post('/scm/shipment/manage/sign', data);
}
export async function getSignDetailApi(params: { id: number }) {
  return requestClient.get('/scm/shipment/manage/sign', { params });
}

export async function confirmShipmentApi(data: SignShipment) {
  return requestClient.post('/scm/shipment/manage/submit', data);
}
