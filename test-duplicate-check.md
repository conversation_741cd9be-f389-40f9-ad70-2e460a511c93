# 企业重复检查功能测试

## 功能说明
在项目管理的企业添加功能中，现在已经实现了重复企业检查，确保：

1. **同一类型内不能重复添加相同企业**：在上游企业、下游企业、授信企业各自的表格中，不能添加相同的企业（根据企业编码 companyCode 判断）

2. **跨类型不能重复添加相同企业**：一个企业不能同时出现在上游企业、下游企业、授信企业的不同表格中

3. **企业选择下拉框自动过滤**：在企业选择模态框中，已经添加的企业不会出现在可选列表中

## 实现细节

### 1. 重复检查函数
- `checkDuplicateCompany()`: 检查单个表格内是否重复
- `checkDuplicateCompanyInAllTypes()`: 检查所有表格中是否重复
- `getAllAddedCompanyCodes()`: 获取所有已添加的企业编码

### 2. 计算属性
- `availableCompanyOptions`: 过滤掉已添加企业的可选企业列表

### 3. 用户体验
- 尝试添加重复企业时显示警告消息："该企业已存在，不能重复添加"
- 企业选择下拉框中不显示已添加的企业
- 删除企业后，该企业会重新出现在可选列表中

## 测试场景

### 场景1：同类型重复添加
1. 在上游企业中添加企业A
2. 再次尝试在上游企业中添加企业A
3. 预期：显示警告消息，不允许添加

### 场景2：跨类型重复添加
1. 在上游企业中添加企业A
2. 尝试在下游企业中添加企业A
3. 预期：显示警告消息，不允许添加

### 场景3：企业选择过滤
1. 在上游企业中添加企业A
2. 打开下游企业的选择企业对话框
3. 预期：企业A不出现在可选列表中

### 场景4：删除后重新可选
1. 在上游企业中添加企业A
2. 删除企业A
3. 打开任意企业选择对话框
4. 预期：企业A重新出现在可选列表中

## 技术实现要点

1. 使用企业编码（companyCode）作为唯一标识进行重复检查
2. 利用Vue的计算属性实现响应式的企业选项过滤
3. 在添加企业前进行重复检查，提供友好的用户提示
4. 确保删除操作后计算属性能正确更新
